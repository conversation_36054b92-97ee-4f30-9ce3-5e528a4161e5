<?php
/**
 * Admin Class
 *
 * @package Q-Updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Q_Updater_Admin
{
    private $parent;

    /**
     * Constructor
     *
     * @param Q_Updater $parent Parent class instance
     */
    public function __construct($parent)
    {
        $this->parent = $parent;

        // Register AJAX handlers


    }



    /**
     * Enqueue admin scripts and styles
     *
     * @param string $hook Current admin page
     */
    public function enqueue_scripts($hook)
    {
        // Load on Q-Updater settings page and plugins page
        if ($hook != 'settings_page_q-updater' && $hook != 'plugins.php')
            return;

        wp_enqueue_style('q-updater-admin', plugins_url('css/admin.css', Q_UPDATER_PLUGIN_FILE), [], '1.0.1');
        wp_enqueue_script('q-updater-admin', plugins_url('js/admin.js', Q_UPDATER_PLUGIN_FILE), ['jquery'], '1.0.1', true);



        // Add Chart.js for analytics
        if ($hook == 'settings_page_q-updater') {
            // Only load Chart.js on the plugin's admin page
            wp_enqueue_script('qu-chartjs', 'https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js', [], '4.4.0', true);

            // Add improved Chart.js loading with multiple CDN fallbacks
            wp_add_inline_script('qu-chartjs', '
                window.quChartJSLoaded = false;
                window.quChartJSAttempts = 0;
                window.quMaxChartJSAttempts = 3;

                // Multiple CDN sources for Chart.js
                const chartJSCDNs = [
                    "https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.min.js",
                    "https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js",
                    "https://unpkg.com/chart.js@4.4.0/dist/chart.min.js"
                ];

                // Function to try loading Chart.js from different CDNs
                function loadChartJSFallback() {
                    if (window.quChartJSAttempts >= window.quMaxChartJSAttempts) {
                        console.error("Chart.js failed to load after maximum attempts from all CDNs");
                        window.dispatchEvent(new CustomEvent("chartjs-load-failed"));
                        return;
                    }

                    const cdnUrl = chartJSCDNs[window.quChartJSAttempts];
                    window.quChartJSAttempts++;
                    console.log("Attempting to load Chart.js from CDN:", cdnUrl, "attempt:", window.quChartJSAttempts);

                    var script = document.createElement("script");
                    script.src = cdnUrl;
                    script.onload = function() {
                        if (typeof Chart !== "undefined") {
                            console.log("Chart.js loaded successfully from:", cdnUrl);
                            window.quChartJSLoaded = true;
                            window.dispatchEvent(new CustomEvent("chartjs-loaded"));
                        } else {
                            console.warn("Chart.js script loaded but Chart object not available");
                            setTimeout(loadChartJSFallback, 1000);
                        }
                    };
                    script.onerror = function() {
                        console.warn("Failed to load Chart.js from:", cdnUrl);
                        setTimeout(loadChartJSFallback, 1000);
                    };
                    document.head.appendChild(script);
                }

                // Check if Chart.js loaded from primary CDN
                window.addEventListener("load", function() {
                    setTimeout(function() {
                        if (typeof Chart !== "undefined") {
                            console.log("Chart.js loaded successfully from primary CDN");
                            window.quChartJSLoaded = true;
                            window.dispatchEvent(new CustomEvent("chartjs-loaded"));
                        } else {
                            console.warn("Chart.js failed to load from primary CDN, trying fallbacks");
                            loadChartJSFallback();
                        }
                    }, 500); // Increased timeout for slower connections
                });
            ');
        }

        // Get CSRF protection instance
        $csrf_protection = $this->parent->get_csrf_protection();

        // Create nonces with enhanced security
        wp_localize_script('q-updater-admin', 'qUpdater', array(
            'ajaxurl' => admin_url('admin-ajax.php'),
            'nonce' => $csrf_protection->create_nonce('q_updater_nonce', 3600), // 1 hour lifetime
            'bulk_update_nonce' => $csrf_protection->create_nonce('bulk_update_q_plugins', 3600), // 1 hour lifetime
            'dismiss_nonce' => $csrf_protection->create_nonce('q_updater_dismiss_notice', 86400), // 24 hour lifetime
            'settings_url' => admin_url('options-general.php?page=q-updater&tab=settings'),
            'review_submit_nonce' => $csrf_protection->create_nonce('qu_submit_review', 1800), // 30 minute lifetime
            'review_delete_nonce' => $csrf_protection->create_nonce('qu_delete_review', 1800), // 30 minute lifetime
            'analytics_nonce' => $csrf_protection->create_nonce('qu_analytics_nonce', 3600), // 1 hour lifetime
            'github_api_debug_nonce' => $csrf_protection->create_nonce('qu_github_api_debug', 3600), // 1 hour lifetime

            'github_debug_url' => admin_url('options-general.php?page=q-updater-github-debug'),
            'csrf_header' => 'X-CSRF-Token', // CSRF header name for AJAX requests
        ));
    }

    /**
     * Show update notices in admin
     */
    public function show_update_notices()
    {
        if (!current_user_can('update_plugins'))
            return;

        $updates = get_site_transient('update_plugins');
        if (empty($updates->response))
            return;

        foreach ($updates->response as $plugin_file => $plugin_data) {
            if (strpos($plugin_file, 'q-') === false)
                continue;

            $plugin_name = dirname($plugin_file);
            printf(
                '<div class="notice notice-warning"><p>' .
                /* translators: 1: plugin name, 2: version number, 3: update URL */
                __('A new version of <strong>%1$s</strong> is available (v%2$s). ', 'q-updater') .
                '<a href="%3$s">' . __('Update now', 'q-updater') . '</a>.' .
                '</p></div>',
                esc_html($plugin_name),
                esc_html($plugin_data->new_version),
                wp_nonce_url(self_admin_url('update.php?action=upgrade-plugin&plugin=' . $plugin_file), 'upgrade-plugin_' . $plugin_file)
            );
        }
    }


}
