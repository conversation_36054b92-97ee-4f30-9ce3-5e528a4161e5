<?php
/**
 * Settings Class
 *
 * @package Q-Updater
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly
}

class Q_Updater_Settings
{
    private $parent;
    private $security;

    /**
     * Constructor
     *
     * @param Q_Updater $parent Parent class instance
     */
    public function __construct($parent)
    {
        $this->parent = $parent;
        $this->security = $parent->get_security();
    }

    /**
     * Add settings page to admin menu
     */
    public function add_settings_page()
    {
        $hook = add_options_page('Q-Updater Settings', 'Q-Updater', 'manage_options', 'q-updater', [$this, 'settings_page']);

        // Add help tabs when the settings page is loaded
        add_action('load-' . $hook, [$this, 'add_help_tabs']);
    }

    /**
     * Add help tabs to the settings page
     */
    public function add_help_tabs()
    {
        $screen = get_current_screen();

        // Overview tab
        $screen->add_help_tab([
            'id' => 'q-updater-overview',
            'title' => 'Overview',
            'content' => '
                <h2>Q-Updater Plugin</h2>
                <p>Q-Updater automatically updates Q plugins from GitHub repositories. This settings page allows you to configure how the plugin works.</p>
                <p>Use the tabs below to navigate between different sections of the plugin:</p>
                <ul>
                    <li><strong>Dashboard</strong> - View plugin statistics and recent updates</li>
                    <li><strong>Plugins</strong> - Manage your Q plugins and check for updates</li>
                    <li><strong>Plugin Manager</strong> - Install new plugins from GitHub and browse available plugins</li>
                    <li><strong>Settings</strong> - Configure GitHub token, update frequency, and notifications</li>
                    <li><strong>Tools</strong> - Roll back to previous versions and access advanced tools</li>
                    <li><strong>Analytics</strong> - View plugin usage and update statistics</li>
                    <li><strong>AI</strong> - Get AI-powered recommendations and assistance</li>
                </ul>
            '
        ]);

        // GitHub Token tab
        $screen->add_help_tab([
            'id' => 'q-updater-github-token',
            'title' => 'GitHub Token',
            'content' => '
                <h2>GitHub Access Token</h2>
                <p>A GitHub Personal Access Token allows Q-Updater to:</p>
                <ul>
                    <li>Access private repositories</li>
                    <li>Avoid GitHub API rate limits</li>
                    <li>Perform more reliable updates</li>
                </ul>
                <p><strong>How to create a token:</strong></p>
                <ol>
                    <li>Go to <a href="https://github.com/settings/tokens/new" target="_blank">GitHub Personal Access Tokens</a></li>
                    <li>Give your token a descriptive name (e.g., "Q-Updater Plugin")</li>
                    <li>Select the "repo" scope to allow access to repositories</li>
                    <li>Click "Generate token" and copy the token</li>
                    <li>Paste the token in the GitHub Access Token field in the settings</li>
                </ol>
                <p><strong>Note:</strong> GitHub will only show the token once, so make sure to copy it immediately after creation.</p>
            '
        ]);

        // Update Frequency tab
        $screen->add_help_tab([
            'id' => 'q-updater-frequency',
            'title' => 'Update Frequency',
            'content' => '
                <h2>Update Check Frequency</h2>
                <p>Control how often Q-Updater checks for plugin updates:</p>
                <ul>
                    <li><strong>Hourly</strong> - Check every hour (may increase server load)</li>
                    <li><strong>Daily</strong> - Check once per day (recommended for most sites)</li>
                    <li><strong>Weekly</strong> - Check once per week</li>
                    <li><strong>Monthly</strong> - Check once per month</li>
                    <li><strong>Manual Only</strong> - Only check when manually triggered</li>
                </ul>
                <p><strong>Recommendation:</strong> For most sites, daily checks provide a good balance between staying up-to-date and minimizing server load.</p>
                <p>You can always manually check for updates regardless of this setting by clicking the "Check for Updates" button on the Plugins tab.</p>
            '
        ]);

        // Notifications tab
        $screen->add_help_tab([
            'id' => 'q-updater-notifications',
            'title' => 'Notifications',
            'content' => '
                <h2>Notification Settings</h2>
                <p>Configure how you want to be notified about updates:</p>
                <h3>Email Notifications</h3>
                <p>Enter an email address to receive notifications when:</p>
                <ul>
                    <li>Updates are available for your Q plugins</li>
                    <li>Plugins have been successfully updated</li>
                </ul>
                <p>Leave this field empty to disable email notifications.</p>

                <h3>Dashboard Notifications</h3>
                <p>When enabled, notifications about available updates will be displayed in the WordPress admin dashboard.</p>
                <p>Disable this option if you prefer to only check for updates manually through the Plugins tab.</p>
            '
        ]);

        // Custom Repository Mappings tab
        $screen->add_help_tab([
            'id' => 'q-updater-custom-repos',
            'title' => 'Custom Repositories',
            'content' => '
                <h2>Custom Repository Mappings</h2>
                <p>By default, Q-Updater assumes all plugins with names starting with "q-" are hosted in GitHub repositories with the format "shamielo/plugin-slug".</p>
                <p>If you have plugins that use different repository paths, you can specify custom mappings in the Security Settings tab.</p>
                <h3>How to use custom repository mappings:</h3>
                <ol>
                    <li>Go to the Security Settings tab</li>
                    <li>Find the "Custom Repository Mappings" section</li>
                    <li>Enter the GitHub repository path for each plugin in the format "username/repository"</li>
                    <li>Leave the field empty to use the default format (shamielo/plugin-slug)</li>
                    <li>Save your settings</li>
                </ol>
                <p><strong>Example:</strong> If your q-whatsapp plugin is hosted at "your-username/whatsapp-plugin", enter that instead of the default.</p>
                <p><strong>Note:</strong> After changing repository mappings, Q-Updater will clear the update cache and check for updates using the new repository paths.</p>
            '
        ]);




    }

    /**
     * Register settings
     */
    public function register_settings()
    {
        register_setting('q_updater_settings', $this->parent->get_option_name('auto_update'));
        register_setting('q_updater_settings', $this->parent->get_option_name('email_notification'));
        register_setting('q_updater_settings', $this->parent->get_option_name('github_token'));
        register_setting('q_updater_settings', $this->parent->get_option_name('update_frequency'));
        register_setting('q_updater_settings', $this->parent->get_option_name('dashboard_notification'));
        register_setting('q_updater_settings', $this->parent->get_option_name('custom_repo_mappings'));
        register_setting('q_updater_settings', $this->parent->get_option_name('developer_email'));
        register_setting('q_updater_settings', $this->parent->get_option_name('analytics'));


        // Token sync settings
        register_setting('q_updater_settings', $this->parent->get_option_name('token_sync_is_master'));
        register_setting('q_updater_settings', $this->parent->get_option_name('token_sync_master_url'));
        register_setting('q_updater_settings', $this->parent->get_option_name('token_sync_key'));
        register_setting('q_updater_settings', $this->parent->get_option_name('token_sync_connected_sites'));
    }

    /**
     * Process developer email update
     */
    private function process_developer_email()
    {
        // Verify user capabilities
        if (!current_user_can('manage_options')) {
            wp_die(__('You do not have sufficient permissions to access this page.', 'q-updater'));
        }

        // Get and sanitize the developer email
        $developer_email = isset($_POST['q_updater_developer_email']) ? sanitize_email($_POST['q_updater_developer_email']) : '';

        // Log for debugging
        error_log('Saving developer email: ' . $developer_email . ' to option: q_updater_developer_email');

        // Update the option directly
        update_option('q_updater_developer_email', $developer_email);

        // Verify it was saved
        $saved_email = get_option('q_updater_developer_email', '');
        error_log('Saved developer email value: ' . $saved_email);

        // Check if weekly analytics reports are enabled
        $analytics_settings = get_option($this->parent->get_option_name('analytics'), ['enabled' => true, 'weekly_report' => true]);

        // If weekly reports are enabled and the email has changed, reschedule the report
        if (!empty($developer_email) && $analytics_settings['weekly_report']) {
            // Get the analytics instance and reschedule the weekly report
            $analytics = $this->parent->get_analytics();
            $analytics->schedule_weekly_report();

            error_log('Weekly analytics report rescheduled after developer email update');
        }

        // Add success message
        add_settings_error(
            'q_updater_settings',
            'developer_email_updated',
            __('Developer email updated successfully.', 'q-updater'),
            'success'
        );
    }

    /**
     * Render settings page
     */
    public function settings_page()
    {
        if ($_POST && check_admin_referer('q_updater_settings', 'q_updater_nonce')) {
            // Verify user capabilities
            if (!current_user_can('manage_options')) {
                wp_die(__('You do not have sufficient permissions to access this page.', 'q-updater'));
            }

            // Check if developer email is being updated
            if (isset($_POST['update_developer_email'])) {
                $this->process_developer_email();
            }

            // Check if GitHub token reset button was clicked
            if (isset($_POST['reset_github_token'])) {
                // Delete the GitHub token
                delete_option($this->parent->get_option_name('github_token'));

                // Log the action
                error_log('Q-Updater: GitHub token has been reset by user');

                // Add success message
                add_settings_error(
                    'q_updater_settings',
                    'token_reset',
                    __('GitHub token has been reset. Please enter a new token.', 'q-updater'),
                    'warning'
                );

                // Redirect to the same page to prevent form resubmission
                wp_redirect(add_query_arg(array('page' => 'q-updater', 'tab' => 'settings'), admin_url('options-general.php')));
                exit;
            }

            $enabled_plugins = isset($_POST['q_updater_auto_update_plugins']) ? array_map('sanitize_text_field', $_POST['q_updater_auto_update_plugins']) : [];
            $email_notifications = isset($_POST['q_updater_email_notifications']) ? sanitize_email($_POST['q_updater_email_notifications']) : '';

            // Handle GitHub token with special care
            $github_token = isset($_POST['q_updater_github_token']) ? sanitize_text_field($_POST['q_updater_github_token']) : '';

            // Only encrypt if the token is not empty and different from the placeholder
            if (!empty($github_token) && $github_token !== '••••••••••••••••••••') {
                // Validate token format before saving
                if ($this->security->validate_github_token($github_token)) {
                    // Encrypt the token before saving
                    $github_token = $this->security->encrypt($github_token);

                    // Set token expiration reminder for 60 days from now
                    $this->security->set_token_expiration(time() + (60 * DAY_IN_SECONDS));

                    // Set token creation date
                    $this->security->set_token_creation(time());

                    // Schedule token renewal reminder
                    $this->security->schedule_token_renewal_reminder(15); // Remind 15 days before expiration

                    // Add success message
                    add_settings_error(
                        'q_updater_settings',
                        'token_saved',
                        __('GitHub token saved successfully. Token will expire in 60 days.', 'q-updater'),
                        'success'
                    );
                } else {
                    // Invalid token format
                    add_settings_error(
                        'q_updater_settings',
                        'invalid_token',
                        __('Invalid GitHub token format. Please enter a valid GitHub Personal Access Token.', 'q-updater'),
                        'error'
                    );

                    // Keep the existing token
                    $github_token = get_option($this->parent->get_option_name('github_token'), '');
                }
            } else if (empty($github_token)) {
                // If token field was emptied, clear the stored token
                $github_token = '';

                // Add notice
                add_settings_error(
                    'q_updater_settings',
                    'token_cleared',
                    __('GitHub token has been cleared. Some features may be limited.', 'q-updater'),
                    'warning'
                );
            } else {
                // If the placeholder was submitted, keep the existing token
                $github_token = get_option($this->parent->get_option_name('github_token'), '');
            }

            $update_frequency = isset($_POST['q_updater_update_frequency']) ? sanitize_text_field($_POST['q_updater_update_frequency']) : 'daily';
            $dashboard_notifications = isset($_POST['q_updater_dashboard_notifications']) ? '1' : '0';

            // Process custom repository mappings
            $custom_repos = isset($_POST['q_updater_custom_repos']) ? $_POST['q_updater_custom_repos'] : [];
            $custom_repo_mappings = [];

            if (!empty($custom_repos)) {
                foreach ($custom_repos as $slug => $repo) {
                    $repo = sanitize_text_field($repo);
                    // Only save if it's a valid repository format and different from the default
                    if (!empty($repo) && $this->security->validate_repository_name($repo) && $repo !== "shamielo/$slug") {
                        $custom_repo_mappings[$slug] = $repo;
                    }
                }
            }

            update_option($this->parent->get_option_name('auto_update'), $enabled_plugins);
            update_option($this->parent->get_option_name('email_notification'), $email_notifications);
            update_option($this->parent->get_option_name('github_token'), $github_token);
            update_option($this->parent->get_option_name('update_frequency'), $update_frequency);
            update_option($this->parent->get_option_name('dashboard_notification'), $dashboard_notifications);
            update_option($this->parent->get_option_name('custom_repo_mappings'), $custom_repo_mappings);

            // Process token sync settings
            $token_sync_is_master = isset($_POST['q_updater_token_sync_is_master']) ? (bool) $_POST['q_updater_token_sync_is_master'] : false;
            $token_sync_master_url = isset($_POST['q_updater_token_sync_master_url']) ? esc_url_raw($_POST['q_updater_token_sync_master_url']) : '';
            $token_sync_key = isset($_POST['q_updater_token_sync_key']) ? sanitize_text_field($_POST['q_updater_token_sync_key']) : '';

            update_option($this->parent->get_option_name('token_sync_is_master'), $token_sync_is_master);
            update_option($this->parent->get_option_name('token_sync_master_url'), $token_sync_master_url);

            // Only update the sync key if it's not empty
            if (!empty($token_sync_key)) {
                update_option($this->parent->get_option_name('token_sync_key'), $token_sync_key);
            }

            // Reschedule update checks based on new frequency
            $this->parent->schedule_update_check();

            // Clear update transients to force fresh checks with new repository mappings
            delete_site_transient('update_plugins');

            add_settings_error('q_updater_settings', 'settings_updated', __('Settings saved successfully.', 'q-updater'), 'success');
        }

        // Process analytics settings if submitted
        $this->process_analytics_settings();

        $active_tab = isset($_GET['tab']) ? sanitize_text_field($_GET['tab']) : 'dashboard';
        $enabled_plugins = get_option($this->parent->get_option_name('auto_update'), []);
        $email_notifications = get_option($this->parent->get_option_name('email_notification'), '');

        // Get the encrypted GitHub token
        $encrypted_token = get_option($this->parent->get_option_name('github_token'), '');

        // For display purposes, we'll just show a placeholder if a token exists
        $github_token = !empty($encrypted_token) ? '••••••••••••••••••••' : '';

        // Check if token might be expired
        if (!empty($encrypted_token)) {
            $token_expiration = $this->security->check_token_expiration($encrypted_token);
            if ($token_expiration && isset($token_expiration['expired']) && $token_expiration['expired']) {
                // Add an admin notice about expired token
                add_settings_error(
                    'q_updater_settings',
                    'token_expired',
                    sprintf(
                        __('Your GitHub token appears to have expired %d days ago. Please generate a new token.', 'q-updater'),
                        $token_expiration['days_ago']
                    ),
                    'warning'
                );
            } else if ($token_expiration && isset($token_expiration['might_be_expired']) && $token_expiration['might_be_expired']) {
                // Add an admin notice about potentially expired token
                add_settings_error(
                    'q_updater_settings',
                    'token_might_be_expired',
                    sprintf(
                        __('Your GitHub token might be expired. No successful API calls in the last %d days.', 'q-updater'),
                        $token_expiration['days_since_last_success']
                    ),
                    'warning'
                );
            }
        }

        $update_frequency = get_option($this->parent->get_option_name('update_frequency'), 'daily');
        $dashboard_notifications = get_option($this->parent->get_option_name('dashboard_notification'), '1');

        $plugin_manager = new Q_Updater_Plugin_Manager($this->parent);
        $updates = new Q_Updater_Updates($this->parent);

        $installed_plugins = $plugin_manager->get_installed_q_plugins();
        $update_logs = get_option($this->parent->get_option_name('log'), []);
        $updates_available = $updates->get_available_updates();
        ?>
        <!-- Skip to content link for accessibility -->
        <a href="#qu-tab-content" class="qu-skip-link"><?php _e('Skip to content', 'q-updater'); ?></a>


        <div class="qu-wrap" id="qu-wrap">


            <div class="qu-header">
                <div class="qu-info">
                    <h1>Q-Updater</h1>
                    <p> Powered by <b>Q-Ai</b></p>
                </div>


                <div class="qu-header-actions">
                    <!-- Help button -->
                    <button type="button" class="qu-help-button" aria-label="Open Help"
                        onclick="jQuery('#contextual-help-link').trigger('click');">
                        <span class="dashicons dashicons-editor-help"></span> Help
                    </button>
                </div>
            </div>




            <nav class="qu-nav-tab-wrapper" role="navigation" aria-label="<?php _e('Main Navigation', 'q-updater'); ?>">
                <a href="#dashboard" data-tab="dashboard"
                    class="qu-nav-tab qu-nav-tab-js <?php echo $active_tab === 'dashboard' ? 'qu-nav-tab-active' : ''; ?>"
                    aria-current="<?php echo $active_tab === 'dashboard' ? 'page' : 'false'; ?>">
                    <span class="dashicons dashicons-dashboard" aria-hidden="true"></span>
                    <span><?php _e('Dashboard', 'q-updater'); ?></span>
                </a>
                <a href="#plugin-manager" data-tab="plugin-manager"
                    class="qu-nav-tab qu-nav-tab-js <?php echo $active_tab === 'plugin-manager' ? 'qu-nav-tab-active' : ''; ?>"
                    aria-current="<?php echo $active_tab === 'plugin-manager' ? 'page' : 'false'; ?>">
                    <span class="dashicons dashicons-admin-plugins" aria-hidden="true"></span>
                    <span><?php _e('Plugin Manager', 'q-updater'); ?></span>
                </a>
                <a href="#settings" data-tab="settings"
                    class="qu-nav-tab qu-nav-tab-js <?php echo $active_tab === 'settings' ? 'qu-nav-tab-active' : ''; ?>"
                    aria-current="<?php echo $active_tab === 'settings' ? 'page' : 'false'; ?>">
                    <span class="dashicons dashicons-admin-settings" aria-hidden="true"></span>
                    <span><?php _e('Settings', 'q-updater'); ?></span>
                </a>
                <a href="#reviews" data-tab="reviews"
                    class="qu-nav-tab qu-nav-tab-js <?php echo $active_tab === 'reviews' ? 'qu-nav-tab-active' : ''; ?>"
                    aria-current="<?php echo $active_tab === 'reviews' ? 'page' : 'false'; ?>">
                    <span class="dashicons dashicons-star-filled" aria-hidden="true"></span>
                    <span><?php _e('Reviews', 'q-updater'); ?></span>
                </a>
                <a href="#tools" data-tab="tools"
                    class="qu-nav-tab qu-nav-tab-js <?php echo $active_tab === 'tools' ? 'qu-nav-tab-active' : ''; ?>"
                    aria-current="<?php echo $active_tab === 'tools' ? 'page' : 'false'; ?>">
                    <span class="dashicons dashicons-admin-tools" aria-hidden="true"></span>
                    <span><?php _e('Tools', 'q-updater'); ?></span>
                </a>
                <a href="#analytics" data-tab="analytics"
                    class="qu-nav-tab qu-nav-tab-js <?php echo $active_tab === 'analytics' ? 'qu-nav-tab-active' : ''; ?>"
                    aria-current="<?php echo $active_tab === 'analytics' ? 'page' : 'false'; ?>">
                    <span class="dashicons dashicons-chart-bar" aria-hidden="true"></span>
                    <span><?php _e('Analytics', 'q-updater'); ?></span>
                </a>

            </nav>

            <div class="qu-tab-content">
                <!-- Dashboard Tab -->
                <div id="dashboard-tab"
                    class="qu-tab-panel <?php echo $active_tab === 'dashboard' ? 'qu-tab-panel-active' : ''; ?>"
                    data-tab="dashboard">
                    <div class="qu-tab-loading" style="display: none;">
                        <div class="qu-loading-spinner"></div>
                        <p><?php _e('Loading dashboard...', 'q-updater'); ?></p>
                    </div>
                    <div class="qu-tab-content-inner">
                        <?php if ($active_tab === 'dashboard'): ?>
                            <?php $this->render_dashboard_tab($installed_plugins, $updates_available, $enabled_plugins, $update_logs); ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Plugin Manager Tab -->
                <div id="plugin-manager-tab"
                    class="qu-tab-panel <?php echo $active_tab === 'plugin-manager' ? 'qu-tab-panel-active' : ''; ?>"
                    data-tab="plugin-manager">
                    <div class="qu-tab-loading" style="display: none;">
                        <div class="qu-loading-spinner"></div>
                        <p><?php _e('Loading plugin manager...', 'q-updater'); ?></p>
                    </div>
                    <div class="qu-tab-content-inner">
                        <?php if ($active_tab === 'plugin-manager'): ?>
                            <?php $this->render_plugin_manager_tab($plugin_manager); ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Settings Tab -->
                <div id="settings-tab"
                    class="qu-tab-panel <?php echo $active_tab === 'settings' ? 'qu-tab-panel-active' : ''; ?>"
                    data-tab="settings">
                    <div class="qu-tab-loading" style="display: none;">
                        <div class="qu-loading-spinner"></div>
                        <p><?php _e('Loading settings...', 'q-updater'); ?></p>
                    </div>
                    <div class="qu-tab-content-inner">
                        <?php if ($active_tab === 'settings'): ?>
                            <?php $this->render_settings_tab($email_notifications, $github_token, $update_frequency, $dashboard_notifications); ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Reviews Tab -->
                <div id="reviews-tab" class="qu-tab-panel <?php echo $active_tab === 'reviews' ? 'qu-tab-panel-active' : ''; ?>"
                    data-tab="reviews">
                    <div class="qu-tab-loading" style="display: none;">
                        <div class="qu-loading-spinner"></div>
                        <p><?php _e('Loading reviews...', 'q-updater'); ?></p>
                    </div>
                    <div class="qu-tab-content-inner">
                        <?php if ($active_tab === 'reviews'): ?>
                            <?php $this->render_reviews_tab($plugin_manager); ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Tools Tab -->
                <div id="tools-tab" class="qu-tab-panel <?php echo $active_tab === 'tools' ? 'qu-tab-panel-active' : ''; ?>"
                    data-tab="tools">
                    <div class="qu-tab-loading" style="display: none;">
                        <div class="qu-loading-spinner"></div>
                        <p><?php _e('Loading tools...', 'q-updater'); ?></p>
                    </div>
                    <div class="qu-tab-content-inner">
                        <?php if ($active_tab === 'tools'): ?>
                            <?php $this->render_tools_tab($plugin_manager); ?>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Analytics Tab -->
                <div id="analytics-tab"
                    class="qu-tab-panel <?php echo $active_tab === 'analytics' ? 'qu-tab-panel-active' : ''; ?>"
                    data-tab="analytics">
                    <div class="qu-tab-loading" style="display: none;">
                        <div class="qu-loading-spinner"></div>
                        <p><?php _e('Loading analytics...', 'q-updater'); ?></p>
                    </div>
                    <div class="qu-tab-content-inner">
                        <?php if ($active_tab === 'analytics'): ?>
                            <?php $this->render_analytics_tab(); ?>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Enhanced Tab Switching JavaScript -->
        <script>
            jQuery(document).ready(function ($) {
                // Tab switching performance optimization
                const QuTabSwitcher = {
                    cache: {},
                    loadedTabs: new Set(['<?php echo esc_js($active_tab); ?>']), // Current tab is already loaded

                    init: function () {
                        this.bindEvents();
                        this.setupInitialState();
                    },

                    bindEvents: function () {
                        $('.qu-nav-tab-js').on('click', this.handleTabClick.bind(this));
                        $(window).on('hashchange', this.handleHashChange.bind(this));
                    },

                    setupInitialState: function () {
                        // Hide all inactive tabs initially
                        $('.qu-tab-panel').not('.qu-tab-panel-active').hide();
                    },

                    handleTabClick: function (e) {
                        e.preventDefault();
                        const $tab = $(e.currentTarget);
                        const tabId = $tab.data('tab');

                        if (tabId) {
                            this.switchToTab(tabId);
                            this.updateURL(tabId);
                        }
                    },

                    handleHashChange: function () {
                        const hash = window.location.hash.replace('#', '');
                        if (hash && this.isValidTab(hash)) {
                            this.switchToTab(hash);
                        }
                    },

                    switchToTab: function (tabId) {
                        // Don't switch if already active
                        const $targetPanel = $('#' + tabId + '-tab');
                        if ($targetPanel.hasClass('qu-tab-panel-active')) {
                            return;
                        }

                        // Update navigation
                        $('.qu-nav-tab').removeClass('qu-nav-tab-active').attr('aria-current', 'false');
                        $(`.qu-nav-tab[data-tab="${tabId}"]`).addClass('qu-nav-tab-active').attr('aria-current', 'page');

                        // Hide current tab
                        $('.qu-tab-panel-active').removeClass('qu-tab-panel-active').fadeOut(150);

                        // Show target tab
                        setTimeout(() => {
                            $targetPanel.addClass('qu-tab-panel-active').fadeIn(200);

                            // Load content if not already loaded
                            if (!this.loadedTabs.has(tabId)) {
                                this.loadTabContent(tabId);
                            }
                        }, 150);
                    },

                    loadTabContent: function (tabId) {
                        const $panel = $('#' + tabId + '-tab');
                        const $loading = $panel.find('.qu-tab-loading');
                        const $content = $panel.find('.qu-tab-content-inner');

                        // Show loading indicator
                        $loading.show();
                        $content.hide();

                        // Make AJAX request to load tab content
                        $.ajax({
                            url: ajaxurl,
                            type: 'POST',
                            data: {
                                action: 'qu_load_tab_content',
                                tab: tabId,
                                nonce: '<?php echo wp_create_nonce('qu_load_tab_nonce'); ?>'
                            },
                            success: (response) => {
                                if (response.success) {
                                    $content.html(response.data.content);
                                    this.loadedTabs.add(tabId);
                                    this.cache[tabId] = response.data.content;

                                    // Initialize tab-specific functionality
                                    this.initializeTabFeatures(tabId);
                                } else {
                                    $content.html('<div class="qu-error">Error loading content: ' + (response.data || 'Unknown error') + '</div>');
                                }
                            },
                            error: () => {
                                $content.html('<div class="qu-error">Failed to load content. Please refresh the page.</div>');
                            },
                            complete: () => {
                                $loading.hide();
                                $content.show();
                            }
                        });
                    },

                    initializeTabFeatures: function (tabId) {
                        // Initialize specific features for each tab
                        switch (tabId) {
                            case 'dashboard':
                                if (typeof initializeDashboardCharts === 'function') {
                                    initializeDashboardCharts();
                                }
                                break;
                            case 'analytics':
                                if (typeof initializeAnalyticsCharts === 'function') {
                                    initializeAnalyticsCharts();
                                }
                                break;
                            case 'plugin-manager':
                                // Reinitialize plugin manager events
                                this.initializePluginManagerEvents();
                                break;
                        }
                    },

                    initializePluginManagerEvents: function () {
                        // Reinitialize plugin manager navigation
                        $('.qu-plugin-manager-nav-item').off('click').on('click', function (e) {
                            e.preventDefault();
                            const targetId = $(this).attr('href');
                            $('.qu-plugin-manager-nav-item').removeClass('active');
                            $(this).addClass('active');
                            $('.qu-plugin-manager-content').hide();
                            $(targetId).fadeIn(300).addClass('qu-fade-in');
                        });
                    },

                    updateURL: function (tabId) {
                        if (history.pushState) {
                            const newUrl = window.location.pathname + window.location.search + '#' + tabId;
                            history.pushState(null, null, newUrl);
                        } else {
                            window.location.hash = tabId;
                        }
                    },

                    isValidTab: function (tabId) {
                        const validTabs = ['dashboard', 'plugin-manager', 'settings', 'reviews', 'tools', 'analytics'];
                        return validTabs.includes(tabId);
                    }
                };

                // Initialize the tab switcher
                QuTabSwitcher.init();

                // Handle initial hash on page load
                const initialHash = window.location.hash.replace('#', '');
                if (initialHash && QuTabSwitcher.isValidTab(initialHash) && initialHash !== '<?php echo esc_js($active_tab); ?>') {
                    QuTabSwitcher.switchToTab(initialHash);
                }
            });
        </script>
        <?php
    }

    /**
     * AJAX handler for loading tab content
     */
    public function ajax_load_tab_content()
    {
        // Verify nonce
        if (!check_ajax_referer('qu_load_tab_nonce', 'nonce', false)) {
            wp_send_json_error(['message' => __('Security check failed.', 'q-updater')], 403);
        }

        // Check user capabilities
        if (!current_user_can('manage_options')) {
            wp_send_json_error(['message' => __('Insufficient permissions.', 'q-updater')], 403);
        }

        $tab = sanitize_text_field($_POST['tab'] ?? '');

        if (empty($tab)) {
            wp_send_json_error(['message' => __('Invalid tab specified.', 'q-updater')]);
        }

        // Start output buffering to capture the tab content
        ob_start();

        try {
            // Load the necessary data for each tab
            switch ($tab) {
                case 'dashboard':
                    $this->load_dashboard_content();
                    break;
                case 'plugin-manager':
                    $this->load_plugin_manager_content();
                    break;
                case 'settings':
                    $this->load_settings_content();
                    break;
                case 'reviews':
                    $this->load_reviews_content();
                    break;
                case 'tools':
                    $this->load_tools_content();
                    break;
                case 'analytics':
                    $this->load_analytics_content();
                    break;
                default:
                    wp_send_json_error(['message' => __('Invalid tab.', 'q-updater')]);
            }

            $content = ob_get_clean();
            wp_send_json_success(['content' => $content]);

        } catch (Exception $e) {
            ob_end_clean();
            error_log('Q-Updater: Error loading tab content for ' . $tab . ': ' . $e->getMessage());
            wp_send_json_error(['message' => __('Error loading content.', 'q-updater')]);
        }
    }

    /**
     * Load dashboard content via AJAX
     */
    private function load_dashboard_content()
    {
        $plugin_manager = new Q_Updater_Plugin_Manager($this->parent);
        $updates = new Q_Updater_Updates($this->parent);

        $installed_plugins = $plugin_manager->get_installed_q_plugins();
        $update_logs = get_option($this->parent->get_option_name('log'), []);
        $updates_available = $updates->get_available_updates();
        $enabled_plugins = get_option($this->parent->get_option_name('enabled_plugins'), []);

        $this->render_dashboard_tab($installed_plugins, $updates_available, $enabled_plugins, $update_logs);
    }

    /**
     * Load plugin manager content via AJAX
     */
    private function load_plugin_manager_content()
    {
        $plugin_manager = new Q_Updater_Plugin_Manager($this->parent);
        $this->render_plugin_manager_tab($plugin_manager);
    }

    /**
     * Load settings content via AJAX
     */
    private function load_settings_content()
    {
        $email_notifications = get_option($this->parent->get_option_name('email_notifications'), '1');
        $github_token = get_option($this->parent->get_option_name('github_token'), '');
        $update_frequency = get_option($this->parent->get_option_name('update_frequency'), 'daily');
        $dashboard_notifications = get_option($this->parent->get_option_name('dashboard_notification'), '1');

        $this->render_settings_tab($email_notifications, $github_token, $update_frequency, $dashboard_notifications);
    }

    /**
     * Load reviews content via AJAX
     */
    private function load_reviews_content()
    {
        $plugin_manager = new Q_Updater_Plugin_Manager($this->parent);
        $this->render_reviews_tab($plugin_manager);
    }

    /**
     * Load tools content via AJAX
     */
    private function load_tools_content()
    {
        $plugin_manager = new Q_Updater_Plugin_Manager($this->parent);
        $this->render_tools_tab($plugin_manager);
    }

    /**
     * Load analytics content via AJAX
     */
    private function load_analytics_content()
    {
        $this->render_analytics_tab();
    }

    private function render_dashboard_tab($installed_plugins, $updates_available, $enabled_plugins, $update_logs)
    {
        // Get analytics data for dashboard with error handling
        $analytics = $this->parent->get_analytics();
        $start_date = date('Y-m-d', strtotime('-30 days'));
        $end_date = date('Y-m-d');

        // Initialize default data in case analytics fails
        $summary_data = [
            'total_installations' => 0,
            'total_updates' => 0,
            'total_rollbacks' => 0,
            'total_uninstallations' => 0,
            'update_success_rate' => 0,
            'successful_updates' => 0
        ];

        $update_frequency_data = [];
        $user_engagement_data = [];

        // Try to get analytics data, but don't fail if it's not available
        try {
            if ($analytics) {
                $summary_data = array_merge($summary_data, $analytics->get_analytics_summary($start_date, $end_date));
                $update_frequency_data = $analytics->get_update_frequency('all', $start_date, $end_date);
                $user_engagement_data = $analytics->get_user_engagement($start_date, $end_date);
            }
        } catch (Exception $e) {
            error_log('Q-Updater: Error loading analytics data for dashboard: ' . $e->getMessage());
        }

        // Get GitHub API status
        $api_status = $this->test_github_connection();
        ?>

        <!-- Dashboard Header -->
        <div class="qu-dashboard-header">
            <div class="qu-dashboard-title">
                <h2> Dashboard Overview</h2>
                <p class="qu-dashboard-subtitle">Complete overview of your Q-Plugins </p>
            </div>
            <div class="qu-dashboard-actions">
                <button type="button" id="qu-check-updates" class="qu-button qu-button-primary">
                    <span class="dashicons dashicons-update"></span> Check for Updates
                </button>
                <button type="button" id="qu-refresh-dashboard" class="qu-button qu-button-secondary">
                    <span class="dashicons dashicons-update-alt"></span> Refresh
                </button>
            </div>
        </div>

        <!-- System Status -->
        <div class="qu-system-status">
            <div class="qu-status-card <?php echo $api_status ? 'qu-status-healthy' : 'qu-status-error'; ?>">
                <div class="qu-status-icon">
                    <span class="dashicons <?php echo $api_status ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                </div>
                <div class="qu-status-info">
                    <h4>GitHub API</h4>
                    <p><?php echo $api_status ? 'Connected' : 'Connection Error'; ?></p>
                </div>
            </div>
            <div class="qu-status-card qu-status-healthy">
                <div class="qu-status-icon">
                    <span class="dashicons dashicons-wordpress"></span>
                </div>
                <div class="qu-status-info">
                    <h4>WordPress</h4>
                    <p>Version <?php echo get_bloginfo('version'); ?></p>
                </div>
            </div>
            <div class="qu-status-card qu-status-healthy">
                <div class="qu-status-icon">
                    <span class="dashicons dashicons-admin-plugins"></span>
                </div>
                <div class="qu-status-info">
                    <h4>Q-Updater</h4>
                    <p>Version <?php echo Q_UPDATER_VERSION; ?></p>
                </div>
            </div>
        </div>

        <!-- Main Stats -->
        <div class="qu-dashboard-stats">
            <div class="qu-stat-card qu-stat-primary">
                <div class="qu-stat-icon">
                    <span class="dashicons dashicons-plugins-checked"></span>
                </div>
                <div class="qu-stat-content">
                    <h3>Total Plugins</h3>
                    <div class="qu-stat-value"><?php echo count($installed_plugins); ?></div>
                    <p class="qu-stat-description">Q plugins installed</p>
                </div>
            </div>
            <div class="qu-stat-card qu-stat-warning">
                <div class="qu-stat-icon">
                    <span class="dashicons dashicons-update"></span>
                </div>
                <div class="qu-stat-content">
                    <h3>Updates Available</h3>
                    <div class="qu-stat-value"><?php echo count($updates_available); ?></div>
                    <p class="qu-stat-description">Plugins need updating</p>
                </div>
            </div>
            <div class="qu-stat-card qu-stat-success">
                <div class="qu-stat-icon">
                    <span class="dashicons dashicons-shield"></span>
                </div>
                <div class="qu-stat-content">
                    <h3>Auto-Update Enabled</h3>
                    <div class="qu-stat-value"><?php echo count($enabled_plugins); ?></div>
                    <p class="qu-stat-description">Plugins auto-updating</p>
                </div>
            </div>
            <div class="qu-stat-card qu-stat-info">
                <div class="qu-stat-icon">
                    <span class="dashicons dashicons-yes-alt"></span>
                </div>
                <div class="qu-stat-content">
                    <h3>Success Rate</h3>
                    <div class="qu-stat-value"><?php echo esc_html($summary_data['update_success_rate']); ?>%</div>
                    <p class="qu-stat-description">Update success rate</p>
                </div>
            </div>
        </div>

        <!-- Analytics Summary -->
        <div class="qu-analytics-summary-dashboard">
            <div class="qu-summary-card">
                <h4><span class="dashicons dashicons-download"></span> Total Installations</h4>
                <div class="qu-summary-value"><?php echo esc_html($summary_data['total_installations']); ?></div>
            </div>
            <div class="qu-summary-card">
                <h4><span class="dashicons dashicons-update"></span> Total Updates</h4>
                <div class="qu-summary-value"><?php echo esc_html($summary_data['total_updates']); ?></div>
            </div>
            <div class="qu-summary-card">
                <h4><span class="dashicons dashicons-undo"></span> Rollbacks</h4>
                <div class="qu-summary-value"><?php echo esc_html($summary_data['total_rollbacks']); ?></div>
            </div>
            <div class="qu-summary-card">
                <h4><span class="dashicons dashicons-trash"></span> Uninstalls</h4>
                <div class="qu-summary-value"><?php echo esc_html($summary_data['total_uninstallations']); ?></div>
            </div>
        </div>

        <!-- Dashboard Charts -->
        <div class="qu-dashboard-charts">
            <div class="qu-chart-card">
                <h3><span class="dashicons dashicons-chart-line"></span> Update Frequency</h3>
                <div class="qu-chart-container">
                    <div class="qu-chart-loading">
                        <div class="qu-loading-spinner"></div>
                        <p>Loading chart...</p>
                    </div>
                    <canvas id="dashboard-update-frequency-chart" style="display: none;"></canvas>
                </div>
            </div>

            <div class="qu-chart-card">
                <h3><span class="dashicons dashicons-chart-bar"></span> Updates by Plugin</h3>
                <div class="qu-chart-container">
                    <div class="qu-chart-loading">
                        <div class="qu-loading-spinner"></div>
                        <p>Loading chart...</p>
                    </div>
                    <canvas id="dashboard-updates-by-plugin-chart" style="display: none;"></canvas>
                </div>
            </div>

            <div class="qu-chart-card">
                <h3><span class="dashicons dashicons-chart-pie"></span> Update Methods</h3>
                <div class="qu-chart-container">
                    <div class="qu-chart-loading">
                        <div class="qu-loading-spinner"></div>
                        <p>Loading chart...</p>
                    </div>
                    <canvas id="dashboard-update-methods-chart" style="display: none;"></canvas>
                </div>
            </div>

            <div class="qu-chart-card">
                <h3><span class="dashicons dashicons-performance"></span> Success Rate</h3>
                <div class="qu-chart-container">
                    <div class="qu-chart-loading">
                        <div class="qu-loading-spinner"></div>
                        <p>Loading chart...</p>
                    </div>
                    <canvas id="dashboard-success-rate-chart" style="display: none;"></canvas>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="qu-dashboard-activity">
            <div class="qu-activity-card">
                <h3><span class="dashicons dashicons-clock"></span> Recent Updates</h3>
                <div class="qu-activity-list">
                    <?php if (!empty($update_logs)): ?>
                        <?php foreach (array_slice($update_logs, 0, 5) as $log): ?>
                            <div class="qu-activity-item">
                                <div class="qu-activity-icon">
                                    <span class="dashicons dashicons-update"></span>
                                </div>
                                <div class="qu-activity-content">
                                    <div class="qu-activity-title"><?php echo esc_html($log['plugin']); ?></div>
                                    <div class="qu-activity-details">
                                        <span class="qu-version-change"><?php echo esc_html($log['old_version']); ?> →
                                            <?php echo esc_html($log['new_version']); ?></span>
                                        <span
                                            class="qu-activity-time"><?php echo esc_html(human_time_diff(strtotime($log['timestamp']), current_time('timestamp'))); ?>
                                            ago</span>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <div class="qu-activity-empty">
                            <span class="dashicons dashicons-info"></span>
                            <p>No recent updates available</p>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="qu-activity-actions">
                    <a href="<?php echo admin_url('options-general.php?page=q-updater&tab=plugin-manager'); ?>"
                        class="qu-button qu-button-secondary">
                        View All Updates
                    </a>
                </div>
            </div>

            <div class="qu-activity-card">
                <h3><span class="dashicons dashicons-admin-tools"></span> Quick Actions</h3>
                <div class="qu-quick-actions">
                    <button type="button" id="qu-bulk-update" class="qu-quick-action-btn" <?php echo count($updates_available) === 0 ? 'disabled' : ''; ?>>
                        <span class="dashicons dashicons-update"></span>
                        <span>Update All (<?php echo count($updates_available); ?>)</span>
                    </button>
                    <button type="button" id="qu-backup-all" class="qu-quick-action-btn">
                        <span class="dashicons dashicons-backup"></span>
                        <span>Backup All Plugins</span>
                    </button>
                    <button type="button" id="qu-health-check" class="qu-quick-action-btn">
                        <span class="dashicons dashicons-heart"></span>
                        <span>Health Check</span>
                    </button>
                    <a href="<?php echo admin_url('options-general.php?page=q-updater&tab=settings'); ?>"
                        class="qu-quick-action-btn">
                        <span class="dashicons dashicons-admin-settings"></span>
                        <span>Settings</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Dashboard JavaScript -->
        <script>
            jQuery(document).ready(function ($) {
                console.log('Dashboard JavaScript loaded');
                console.log('Summary data:', <?php echo json_encode($summary_data); ?>);

                // Initialize dashboard charts
                initializeDashboardCharts();

                // Dashboard action handlers
                $('#qu-check-updates').on('click', function () {
                    checkForUpdates();
                });

                $('#qu-refresh-dashboard').on('click', function () {
                    location.reload();
                });

                $('#qu-bulk-update').on('click', function () {
                    if (confirm('Are you sure you want to update all plugins?')) {
                        bulkUpdatePlugins();
                    }
                });

                $('#qu-backup-all').on('click', function () {
                    backupAllPlugins();
                });

                $('#qu-health-check').on('click', function () {
                    performHealthCheck();
                });

                // Dashboard chart initialization function
                function initializeDashboardCharts() {
                    console.log('Initializing dashboard charts...');

                    // Wait for Chart.js to be loaded with improved error handling
                    function waitForChartJS(callback, maxAttempts = 100) {
                        let attempts = 0;
                        const checkChart = () => {
                            console.log('Checking for Chart.js, attempt:', attempts + 1);
                            if (typeof Chart !== 'undefined') {
                                console.log('Chart.js found, version:', Chart.version || 'unknown');
                                callback();
                            } else if (attempts < maxAttempts) {
                                attempts++;
                                setTimeout(checkChart, 200); // Increased timeout
                            } else {
                                console.error('Chart.js failed to load after maximum attempts');
                                showChartError('Chart.js library failed to load. Please check your internet connection or contact support.');
                            }
                        };

                        // Listen for custom Chart.js loading events
                        window.addEventListener('chartjs-loaded', () => {
                            console.log('Chart.js loaded via custom event');
                            callback();
                        });

                        window.addEventListener('chartjs-load-failed', () => {
                            console.error('Chart.js loading failed via custom event');
                            showChartError('Chart.js library failed to load from all sources.');
                        });

                        checkChart();
                    }

                    // Show error message in chart containers with retry option
                    function showChartError(message) {
                        const chartContainers = document.querySelectorAll('.qu-chart-container');
                        chartContainers.forEach(container => {
                            container.innerHTML = `
                                <div class="qu-chart-error">
                                    <span class="dashicons dashicons-warning" style="color: #d63638; font-size: 24px;"></span>
                                    <p style="margin: 10px 0; color: #646970;">${message}</p>
                                    <button type="button" class="qu-button qu-button-secondary qu-retry-charts" style="margin-top: 10px;">
                                        <span class="dashicons dashicons-update"></span> Retry Loading Charts
                                    </button>
                                </div>
                            `;
                        });

                        // Add retry functionality
                        document.querySelectorAll('.qu-retry-charts').forEach(button => {
                            button.addEventListener('click', function () {
                                this.disabled = true;
                                this.innerHTML = '<span class="dashicons dashicons-update spin"></span> Retrying...';

                                // Clear any existing Chart.js
                                if (typeof Chart !== 'undefined') {
                                    delete window.Chart;
                                }

                                // Reload the page to retry Chart.js loading
                                setTimeout(() => {
                                    location.reload();
                                }, 1000);
                            });
                        });
                    }

                    // Initialize charts once Chart.js is ready
                    waitForChartJS(() => {
                        // Hide loading indicators and show canvases
                                document.querySelectorAll('.qu-chart-loading').forEach(loader => {
                                    loader.style.display = 'none';
                                });

                                // Get chart elements
                                const updateFrequencyChart = document.getElementById('dashboard-update-frequency-chart');
                                const updatesByPluginChart = document.getElementById('dashboard-updates-by-plugin-chart');
                                const updateMethodsChart = document.getElementById('dashboard-update-methods-chart');
                                const successRateChart = document.getElementById('dashboard-success-rate-chart');

                                if (!updateFrequencyChart || !updatesByPluginChart || !updateMethodsChart || !successRateChart) {
                                    console.error('One or more dashboard chart elements not found');
                                    showChartError('Chart elements not found');
                                    return;
                                }

                                // Show the canvas elements
                                updateFrequencyChart.style.display = 'block';
                                updatesByPluginChart.style.display = 'block';
                                updateMethodsChart.style.display = 'block';
                                successRateChart.style.display = 'block';

                                // Initialize success rate chart first with existing data
                                createSuccessRateChart(successRateChart, {
                                    success_rate: <?php echo esc_js($summary_data['update_success_rate']); ?>,
                                    total_updates: <?php echo esc_js($summary_data['total_updates']); ?>,
                                    successful_updates: <?php echo esc_js($summary_data['successful_updates']); ?>
                                });

                                // Load analytics data for update frequency and plugin charts
                                $.ajax({
                                    url: typeof ajaxurl !== 'undefined' ? ajaxurl : '<?php echo admin_url('admin-ajax.php'); ?>',
                                    type: 'POST',
                                    data: {
                                        action: 'qu_get_analytics_data',
                                        nonce: '<?php echo wp_create_nonce('qu_analytics_nonce'); ?>',
                                        start_date: '<?php echo esc_js($start_date); ?>',
                                        end_date: '<?php echo esc_js($end_date); ?>',
                                        data_type: 'update_frequency'
                                    },
                                    success: function (response) {
                                        if (response.success && response.data) {
                                            createUpdateFrequencyChart(updateFrequencyChart, response.data);
                                            createUpdatesByPluginChart(updatesByPluginChart, response.data);
                                        } else {
                                            console.warn('No update frequency data available');
                                            createUpdateFrequencyChart(updateFrequencyChart, { monthly: [], by_plugin: [] });
                                            createUpdatesByPluginChart(updatesByPluginChart, { monthly: [], by_plugin: [] });
                                        }
                                    },
                                    error: function (xhr, status, error) {
                                        console.error('Error loading update frequency data:', error);
                                        createUpdateFrequencyChart(updateFrequencyChart, { monthly: [], by_plugin: [] });
                                        createUpdatesByPluginChart(updatesByPluginChart, { monthly: [], by_plugin: [] });
                                    }
                                });

                                // Load user engagement data for update methods chart
                                $.ajax({
                                    url: typeof ajaxurl !== 'undefined' ? ajaxurl : '<?php echo admin_url('admin-ajax.php'); ?>',
                                    type: 'POST',
                                    data: {
                                        action: 'qu_get_analytics_data',
                                        nonce: '<?php echo wp_create_nonce('qu_analytics_nonce'); ?>',
                                        start_date: '<?php echo esc_js($start_date); ?>',
                                        end_date: '<?php echo esc_js($end_date); ?>',
                                        data_type: 'user_engagement'
                                    },
                                    success: function (response) {
                                        if (response.success && response.data) {
                                            createUpdateMethodsChart(updateMethodsChart, response.data);
                                        } else {
                                            console.warn('No user engagement data available');
                                            createUpdateMethodsChart(updateMethodsChart, { update_methods: { auto: 0, manual: 0 } });
                                        }
                                    },
                                    error: function (xhr, status, error) {
                                        console.error('Error loading user engagement data:', error);
                                        createUpdateMethodsChart(updateMethodsChart, { update_methods: { auto: 0, manual: 0 } });
                                    }
                                });
                            });
                        }

                        // Chart creation functions
                        function createUpdateFrequencyChart(canvas, data) {
                            const ctx = canvas.getContext('2d');
                            const monthlyData = data.monthly || [];

                            // Generate default data if no data available
                            let labels, chartData;
                            if (monthlyData.length === 0) {
                                // Generate last 6 months with zero data
                                labels = [];
                                chartData = [];
                                for (let i = 5; i >= 0; i--) {
                                    const date = new Date();
                                    date.setMonth(date.getMonth() - i);
                                    labels.push(date.toISOString().slice(0, 7)); // YYYY-MM format
                                    chartData.push(0);
                                }
                            } else {
                                labels = monthlyData.map(item => item.month);
                                chartData = monthlyData.map(item => parseInt(item.count) || 0);
                            }

                            new Chart(ctx, {
                                type: 'line',
                                data: {
                                    labels: labels,
                                    datasets: [{
                                        label: 'Updates',
                                        data: chartData,
                                        borderColor: '#0073aa',
                                        backgroundColor: 'rgba(0, 115, 170, 0.1)',
                                        borderWidth: 2,
                                        fill: true,
                                        tension: 0.4,
                                        pointBackgroundColor: '#0073aa',
                                        pointBorderColor: '#ffffff',
                                        pointBorderWidth: 2,
                                        pointRadius: 4
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    plugins: {
                                        legend: {
                                            display: false
                                        },
                                        tooltip: {
                                            mode: 'index',
                                            intersect: false,
                                            callbacks: {
                                                title: function (context) {
                                                    return 'Month: ' + context[0].label;
                                                },
                                                label: function (context) {
                                                    return 'Updates: ' + context.parsed.y;
                                                }
                                            }
                                        }
                                    },
                                    scales: {
                                        y: {
                                            beginAtZero: true,
                                            ticks: {
                                                precision: 0,
                                                callback: function (value) {
                                                    return Number.isInteger(value) ? value : '';
                                                }
                                            },
                                            grid: {
                                                color: 'rgba(0, 0, 0, 0.1)'
                                            }
                                        },
                                        x: {
                                            grid: {
                                                display: false
                                            }
                                        }
                                    },
                                    interaction: {
                                        intersect: false,
                                        mode: 'index'
                                    }
                                }
                            });
                        }

                        function createUpdatesByPluginChart(canvas, data) {
                            const ctx = canvas.getContext('2d');
                            const pluginData = data.by_plugin || [];

                            // Handle empty data
                            let labels, chartData, backgroundColors;
                            if (pluginData.length === 0) {
                                labels = ['No Data Available'];
                                chartData = [0];
                                backgroundColors = ['#e0e0e0'];
                            } else {
                                const topPlugins = pluginData.slice(0, 5); // Show top 5 plugins
                                labels = topPlugins.map(item => {
                                    // Truncate long plugin names
                                    const name = item.plugin_slug || 'Unknown';
                                    return name.length > 15 ? name.substring(0, 15) + '...' : name;
                                });
                                chartData = topPlugins.map(item => parseInt(item.count) || 0);
                                backgroundColors = [
                                    '#0073aa',
                                    '#00a32a',
                                    '#dba617',
                                    '#d63638',
                                    '#8c8f94'
                                ].slice(0, topPlugins.length);
                            }

                            new Chart(ctx, {
                                type: 'bar',
                                data: {
                                    labels: labels,
                                    datasets: [{
                                        label: 'Updates',
                                        data: chartData,
                                        backgroundColor: backgroundColors,
                                        borderWidth: 0,
                                        borderRadius: 4,
                                        borderSkipped: false
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    plugins: {
                                        legend: {
                                            display: false
                                        },
                                        tooltip: {
                                            callbacks: {
                                                title: function (context) {
                                                    // Show full plugin name in tooltip
                                                    const index = context[0].dataIndex;
                                                    return pluginData[index] ? pluginData[index].plugin_slug : context[0].label;
                                                },
                                                label: function (context) {
                                                    return 'Updates: ' + context.parsed.y;
                                                }
                                            }
                                        }
                                    },
                                    scales: {
                                        y: {
                                            beginAtZero: true,
                                            ticks: {
                                                precision: 0,
                                                callback: function (value) {
                                                    return Number.isInteger(value) ? value : '';
                                                }
                                            },
                                            grid: {
                                                color: 'rgba(0, 0, 0, 0.1)'
                                            }
                                        },
                                        x: {
                                            grid: {
                                                display: false
                                            },
                                            ticks: {
                                                maxRotation: 45,
                                                minRotation: 0
                                            }
                                        }
                                    }
                                }
                            });
                        }

                        function createUpdateMethodsChart(canvas, data) {
                            const ctx = canvas.getContext('2d');
                            const updateMethods = data.update_methods || { auto: 0, manual: 0 };

                            const autoCount = parseInt(updateMethods.auto) || 0;
                            const manualCount = parseInt(updateMethods.manual) || 0;
                            const total = autoCount + manualCount;

                            // Handle case where there's no data
                            let chartData, labels, colors;
                            if (total === 0) {
                                chartData = [1];
                                labels = ['No Data Available'];
                                colors = ['#e0e0e0'];
                            } else {
                                chartData = [autoCount, manualCount];
                                labels = ['Automatic', 'Manual'];
                                colors = ['#00a32a', '#0073aa'];
                            }

                            new Chart(ctx, {
                                type: 'doughnut',
                                data: {
                                    labels: labels,
                                    datasets: [{
                                        data: chartData,
                                        backgroundColor: colors,
                                        borderWidth: 2,
                                        borderColor: '#ffffff',
                                        hoverBorderWidth: 3,
                                        hoverBorderColor: '#ffffff'
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    cutout: '60%',
                                    plugins: {
                                        legend: {
                                            position: 'bottom',
                                            labels: {
                                                padding: 20,
                                                usePointStyle: true,
                                                font: {
                                                    size: 12
                                                }
                                            }
                                        },
                                        tooltip: {
                                            callbacks: {
                                                label: function (context) {
                                                    if (total === 0) {
                                                        return 'No updates recorded';
                                                    }
                                                    const percentage = ((context.parsed / total) * 100).toFixed(1);
                                                    return context.label + ': ' + context.parsed + ' (' + percentage + '%)';
                                                }
                                            }
                                        }
                                    }
                                }
                            });
                        }

                        function createSuccessRateChart(canvas, data) {
                            const ctx = canvas.getContext('2d');
                            const successRate = parseFloat(data.success_rate) || 0;
                            const totalUpdates = parseInt(data.total_updates) || 0;
                            const successfulUpdates = parseInt(data.successful_updates) || 0;

                            // Handle case where there's no data
                            let chartData, labels, colors;
                            if (totalUpdates === 0) {
                                chartData = [1];
                                labels = ['No Updates'];
                                colors = ['#e0e0e0'];
                            } else {
                                const failureRate = 100 - successRate;
                                chartData = [successRate, failureRate];
                                labels = ['Success', 'Failed'];
                                colors = ['#00a32a', '#d63638'];
                            }

                            new Chart(ctx, {
                                type: 'doughnut',
                                data: {
                                    labels: labels,
                                    datasets: [{
                                        data: chartData,
                                        backgroundColor: colors,
                                        borderWidth: 3,
                                        borderColor: '#ffffff',
                                        hoverBorderWidth: 4,
                                        hoverBorderColor: '#ffffff'
                                    }]
                                },
                                options: {
                                    responsive: true,
                                    maintainAspectRatio: false,
                                    cutout: '65%',
                                    plugins: {
                                        legend: {
                                            position: 'bottom',
                                            labels: {
                                                padding: 20,
                                                usePointStyle: true,
                                                font: {
                                                    size: 12
                                                }
                                            }
                                        },
                                        tooltip: {
                                            callbacks: {
                                                label: function (context) {
                                                    if (totalUpdates === 0) {
                                                        return 'No updates recorded yet';
                                                    }
                                                    const percentage = context.parsed.toFixed(1);
                                                    const count = context.label === 'Success' ? successfulUpdates : (totalUpdates - successfulUpdates);
                                                    return context.label + ': ' + count + ' updates (' + percentage + '%)';
                                                }
                                            }
                                        }
                                    }
                                }
                            });
                        }

                        // Dashboard action functions
                        function checkForUpdates() {
                            const button = $('#qu-check-updates');
                            const originalText = button.html();

                            button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Checking...');

                            $.ajax({
                                url: typeof ajaxurl !== 'undefined' ? ajaxurl : '<?php echo admin_url('admin-ajax.php'); ?>',
                                type: 'POST',
                                data: {
                                    action: 'check_for_updates',
                                    nonce: '<?php echo wp_create_nonce('qu_check_updates'); ?>'
                                },
                                success: function (response) {
                                    if (response.success) {
                                        location.reload();
                                    } else {
                                        alert('Error checking for updates: ' + (response.data || 'Unknown error'));
                                    }
                                },
                                error: function () {
                                    alert('Error checking for updates. Please try again.');
                                },
                                complete: function () {
                                    button.prop('disabled', false).html(originalText);
                                }
                            });
                        }

                        function bulkUpdatePlugins() {
                            const button = $('#qu-bulk-update');
                            const originalText = button.html();

                            button.prop('disabled', true).html('<span class="dashicons dashicons-update spin"></span> Updating...');

                            $.ajax({
                                url: typeof ajaxurl !== 'undefined' ? ajaxurl : '<?php echo admin_url('admin-ajax.php'); ?>',
                                type: 'POST',
                                data: {
                                    action: 'bulk_update_plugins',
                                    nonce: '<?php echo wp_create_nonce('qu_bulk_update'); ?>'
                                },
                                success: function (response) {
                                    if (response.success) {
                                        alert('Bulk update completed successfully!');
                                        location.reload();
                                    } else {
                                        alert('Error during bulk update: ' + (response.data || 'Unknown error'));
                                    }
                                },
                                error: function () {
                                    alert('Error during bulk update. Please try again.');
                                },
                                complete: function () {
                                    button.prop('disabled', false).html(originalText);
                                }
                            });
                        }

                        function backupAllPlugins() {
                            const button = $('#qu-backup-all');
                            const originalText = button.html();

                            button.prop('disabled', true).html('<span class="dashicons dashicons-backup spin"></span> Backing up...');

                            $.ajax({
                                url: typeof ajaxurl !== 'undefined' ? ajaxurl : '<?php echo admin_url('admin-ajax.php'); ?>',
                                type: 'POST',
                                data: {
                                    action: 'backup_all_plugins',
                                    nonce: '<?php echo wp_create_nonce('qu_backup_all'); ?>'
                                },
                                success: function (response) {
                                    if (response.success) {
                                        alert('All plugins backed up successfully!');
                                    } else {
                                        alert('Error during backup: ' + (response.data || 'Unknown error'));
                                    }
                                },
                                error: function () {
                                    alert('Error during backup. Please try again.');
                                },
                                complete: function () {
                                    button.prop('disabled', false).html(originalText);
                                }
                            });
                        }

                        function performHealthCheck() {
                            const button = $('#qu-health-check');
                            const originalText = button.html();

                            button.prop('disabled', true).html('<span class="dashicons dashicons-heart spin"></span> Checking...');

                            $.ajax({
                                url: typeof ajaxurl !== 'undefined' ? ajaxurl : '<?php echo admin_url('admin-ajax.php'); ?>',
                                type: 'POST',
                                data: {
                                    action: 'perform_health_check',
                                    nonce: '<?php echo wp_create_nonce('qu_health_check'); ?>'
                                },
                                success: function (response) {
                                    if (response.success) {
                                        alert('Health check completed: ' + response.data);
                                    } else {
                                        alert('Health check failed: ' + (response.data || 'Unknown error'));
                                    }
                                },
                                error: function () {
                                    alert('Error during health check. Please try again.');
                                },
                                complete: function () {
                                    button.prop('disabled', false).html(originalText);
                                }
                            });
                        }
                    });
                </script>
                <?php
    }

    /**
     * Test GitHub API connection
     *
     * @return bool True if connection is successful, false otherwise
     */
    private function test_github_connection()
    {
        try {
            // Try to get the token manager and test the token
            $token_manager = $this->parent->get_token_manager();
            if (!$token_manager) {
                return false;
            }

            $token_test = $token_manager->test_token();
            if ($token_test['status'] === 'error') {
                return false;
            }

            // Try a simple API request to test connectivity
            $github_api = $this->parent->get_github_api();
            if (!$github_api) {
                return false;
            }

            $headers = $github_api->get_github_headers();
            $response = wp_remote_get('https://api.github.com', [
                'headers' => $headers,
                'timeout' => 10,
                'sslverify' => true
            ]);

            if (is_wp_error($response)) {
                return false;
            }

            $response_code = wp_remote_retrieve_response_code($response);
            return $response_code === 200;

        } catch (Exception $e) {
            error_log('Q-Updater: GitHub connection test failed: ' . $e->getMessage());
            return false;
        }
    }



    private function render_settings_tab($email_notifications, $github_token, $update_frequency = 'daily', $dashboard_notifications = '1')
    {
        // Use the parameters or get from options if not provided
        $update_frequency = $update_frequency ?: get_option($this->parent->get_option_name('update_frequency'), 'daily');
        $dashboard_notifications = $dashboard_notifications ?: get_option($this->parent->get_option_name('dashboard_notification'), '1');

        // Get additional data for the settings summary
        $plugin_manager = new Q_Updater_Plugin_Manager($this->parent);
        $installed_plugins = $plugin_manager->get_installed_q_plugins();
        $auto_update_plugins = get_option($this->parent->get_option_name('auto_update'), []);
        $token_status = !empty($github_token) ? 'configured' : 'not-configured';
        $last_check = get_option($this->parent->get_option_name('last_check'), 0);
        $next_check = wp_next_scheduled('q_updater_check_updates');
        ?>


                <!-- Settings Summary -->
                <div class="qu-settings-summary">
                    <div class="qu-summary-card">
                        <h4 class="qu-summary-card-title">GitHub Token</h4>
                        <div class="qu-summary-card-value">
                            <span
                                class="dashicons <?php echo $token_status === 'configured' ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                            <?php echo $token_status === 'configured' ? 'Configured' : 'Not Configured'; ?>
                        </div>
                        <div class="qu-summary-card-status <?php echo $token_status === 'configured' ? '' : 'warning'; ?>">
                            <?php echo $token_status === 'configured' ? 'Token is set and ready to use' : 'Configure a token for better access'; ?>
                        </div>
                    </div>

                    <div class="qu-summary-card">
                        <h4 class="qu-summary-card-title">Update Schedule</h4>
                        <div class="qu-summary-card-value">
                            <span class="dashicons dashicons-calendar-alt"></span>
                            <?php echo ucfirst($update_frequency); ?>
                        </div>
                        <div class="qu-summary-card-status">
                            <?php
                            if ($update_frequency === 'manual') {
                                echo 'Manual checks only';
                            } else {
                                echo $next_check ? 'Next check: ' . human_time_diff(time(), $next_check) : 'Not scheduled';
                            }
                            ?>
                        </div>
                    </div>

                    <div class="qu-summary-card">
                        <h4 class="qu-summary-card-title">Auto-Update Plugins</h4>
                        <div class="qu-summary-card-value">
                            <span class="dashicons dashicons-shield"></span>
                            <?php echo count($auto_update_plugins); ?> / <?php echo count($installed_plugins); ?>
                        </div>
                        <div class="qu-summary-card-status">
                            <?php echo count($auto_update_plugins) > 0 ? 'Auto-updates enabled' : 'No plugins set for auto-update'; ?>
                        </div>
                    </div>
                </div>

                <div class="qu-plugin-manager-section">
                    <!-- Settings Navigation -->
                    <div class="qu-settings-nav">
                        <a href="#general-settings" class="qu-settings-nav-item active">
                            General
                        </a>
                        <a href="#update-settings" class="qu-settings-nav-item">
                            Updates
                        </a>
                        <a href="#notification-settings" class="qu-settings-nav-item">
                            Notifications
                        </a>
                        <a href="#security-settings" class="qu-settings-nav-item">
                            Security
                        </a>
                        <a href="#token-sync-settings" class="qu-settings-nav-item">
                            Token Sync
                        </a>
                    </div>


                    <div class="qu-settings-card">
                        <form method="post" action="" class="qu-settings-form">
                            <?php
                            settings_fields('q_updater_settings');
                            wp_nonce_field('q_updater_settings', 'q_updater_nonce');

                            settings_errors('q_updater_settings');
                            ?>




                            <!-- General Settings Section -->
                            <div id="general-settings" class="qu-settings-section">
                                <h2><span class="dashicons dashicons-admin-generic"></span> General Settings</h2>

                                <div class="qu-settings-form-group">
                                    <label>
                                        GitHub Access Token
                                        <span class="qu-settings-tooltip">
                                            <span class="dashicons dashicons-editor-help"></span>
                                            <span class="qu-tooltip-content">
                                                A GitHub Personal Access Token allows Q-Updater to access private repositories and
                                                increases the API rate limit. Create a token with the "repo" scope for full access.
                                                Your token will be securely encrypted before being stored in the database.
                                            </span>
                                        </span>
                                    </label>
                                    <input type="password" name="q_updater_github_token" value="<?php echo esc_attr($github_token); ?>"
                                        class="regular-text" autocomplete="new-password" />
                                    <p class="description">
                                        <a href="https://github.com/settings/tokens/new" target="_blank">
                                            Create a GitHub Personal Access Token
                                        </a> (requires "repo" scope for private repositories)
                                    </p>
                                    <div class="qu-token-security-info">
                                        <span class="dashicons dashicons-shield"></span>
                                        <span>Your token is securely encrypted before being stored in the database.</span>
                                    </div>
                                    <?php if (!empty($encrypted_token)): ?>
                                            <div class="qu-token-expiration-info">
                                                <span class="dashicons dashicons-calendar-alt"></span>
                                                <span>GitHub tokens should be refreshed periodically for security. Consider generating a new
                                                    token
                                                    every 60-90 days.</span>
                                            </div>
                                            <div class="qu-token-actions" style="margin-top: 10px;">
                                                <button type="submit" name="reset_github_token" class="button button-secondary"
                                                    onclick="return confirm('Are you sure you want to clear the GitHub token? This will remove the token from the database and you will need to enter a new one.');">
                                                    <span class="dashicons dashicons-trash" style="margin-top: 3px;"></span>
                                                    Reset GitHub Token
                                                </button>
                                                <p class="description" style="margin-top: 5px;">
                                                    If you're having issues with GitHub API authentication, try resetting the token and entering
                                                    it
                                                    again.
                                                </p>
                                            </div>
                                    <?php endif; ?>
                                </div>

                            </div>

                            <!-- Update Settings Section -->
                            <div id="update-settings" class="qu-settings-section" style="display: none;">
                                <h2><span class="dashicons dashicons-update"></span> Update Settings</h2>

                                <div class="qu-settings-form-group">
                                    <label>
                                        Update Check Frequency
                                        <span class="qu-settings-tooltip">
                                            <span class="dashicons dashicons-editor-help"></span>
                                            <span class="qu-tooltip-content">
                                                Control how often Q-Updater checks for plugin updates. More frequent checks may increase
                                                server load.
                                            </span>
                                        </span>
                                    </label>



                                    <div class="qu-frequency-selector">
                                        <div class="qu-frequency-option">
                                            <input type="radio" id="frequency-hourly" name="q_updater_update_frequency" value="hourly"
                                                <?php checked($update_frequency, 'hourly'); ?>>
                                            <label for="frequency-hourly">
                                                Hourly
                                            </label>
                                        </div>
                                        <div class="qu-frequency-option">
                                            <input type="radio" id="frequency-daily" name="q_updater_update_frequency" value="daily"
                                                <?php checked($update_frequency, 'daily'); ?>>
                                            <label for="frequency-daily">
                                                Daily
                                            </label>
                                        </div>
                                        <div class="qu-frequency-option">
                                            <input type="radio" id="frequency-weekly" name="q_updater_update_frequency" value="weekly"
                                                <?php checked($update_frequency, 'weekly'); ?>>
                                            <label for="frequency-weekly">
                                                Weekly
                                            </label>
                                        </div>
                                        <div class="qu-frequency-option">
                                            <input type="radio" id="frequency-monthly" name="q_updater_update_frequency" value="monthly"
                                                <?php checked($update_frequency, 'monthly'); ?>>
                                            <label for="frequency-monthly">
                                                Monthly
                                            </label>
                                        </div>
                                        <div class="qu-frequency-option">
                                            <input type="radio" id="frequency-manual" name="q_updater_update_frequency" value="manual"
                                                <?php checked($update_frequency, 'manual'); ?>>
                                            <label for="frequency-manual">
                                                Manual Only
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="qu-settings-form-group">
                                    <label>
                                        Auto-Update Plugins
                                        <span class="qu-settings-tooltip">
                                            <span class="dashicons dashicons-editor-help"></span>
                                            <span class="qu-tooltip-content">
                                                Select which plugins should be automatically updated when new versions are available.
                                            </span>
                                        </span>
                                    </label>

                                    <div class="qu-auto-update-plugins">
                                        <?php
                                        // Get installed plugins
                                        $installed_plugins = $plugin_manager->get_installed_q_plugins();

                                        if (empty($installed_plugins)) {
                                            echo '<p class="qu-no-plugins-message">No Q plugins are currently installed.</p>';
                                        } else {
                                            echo '<div class="qu-plugins-grid qu-auto-update-grid">';

                                            foreach ($installed_plugins as $slug => $repo) {
                                                $plugin_data = $plugin_manager->get_safe_plugin_data("$slug/$slug.php");
                                                if (!$plugin_data)
                                                    continue;

                                                $is_checked = in_array($slug, $auto_update_plugins);
                                                ?>
                                                        <div class="qu-plugin-auto-update-item">
                                                            <div class="qu-checkbox-wrapper">
                                                                <input type="checkbox" id="auto_update_<?php echo esc_attr($slug); ?>"
                                                                    name="q_updater_auto_update_plugins[]" value="<?php echo esc_attr($slug); ?>" <?php checked($is_checked); ?>>
                                                                <label for="auto_update_<?php echo esc_attr($slug); ?>">
                                                                    <?php echo esc_html($plugin_data['Name']); ?>
                                                                </label>
                                                            </div>

                                                        </div>
                                                        <?php
                                            }

                                            echo '</div>';
                                        }
                                        ?>
                                    </div>
                                </div>

                                <div class="qu-settings-info-box">
                                    <span class="dashicons dashicons-info"></span>
                                    <div class="qu-settings-info-content">
                                        <div class="qu-settings-info-title">Recommendation</div>
                                        <div class="qu-settings-info-description">
                                            For most sites, daily checks provide a good balance between staying up-to-date and
                                            minimizing
                                            server load.
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Notification Settings Section -->
                            <div id="notification-settings" class="qu-settings-section" style="display: none;">
                                <h2><span class="dashicons dashicons-megaphone"></span> Notification Settings</h2>

                                <div class="qu-settings-form-group">
                                    <label>
                                        Email Notifications
                                        <span class="qu-settings-tooltip">
                                            <span class="dashicons dashicons-editor-help"></span>
                                            <span class="qu-tooltip-content">
                                                Receive email notifications when updates are available or when plugins are automatically
                                                updated.
                                            </span>
                                        </span>
                                    </label>
                                    <input type="email" name="q_updater_email_notifications"
                                        value="<?php echo esc_attr($email_notifications); ?>" class="regular-text"
                                        placeholder="<EMAIL>" />
                                    <p class="description">
                                        Enter an email address to receive update notifications. Leave empty to disable email
                                        notifications.
                                    </p>
                                </div>

                                <div class="qu-settings-form-group">
                                    <label>
                                        Dashboard Notifications
                                        <span class="qu-settings-tooltip">
                                            <span class="dashicons dashicons-editor-help"></span>
                                            <span class="qu-tooltip-content">
                                                Show notifications in the WordPress admin dashboard when updates are available.
                                            </span>
                                        </span>
                                    </label>
                                    <div class="qu-checkbox-wrapper">
                                        <input type="checkbox" id="dashboard-notifications" name="q_updater_dashboard_notifications"
                                            value="1" <?php checked($dashboard_notifications, '1'); ?>>
                                        <label for="dashboard-notifications">Enable dashboard notifications</label>
                                    </div>
                                    <p class="description">
                                        Show notifications in the WordPress admin dashboard when updates are available.
                                    </p>
                                </div>
                            </div>

                            <!-- Security Settings Section -->
                            <div id="security-settings" class="qu-settings-section" style="display: none;">
                                <h2><span class="dashicons dashicons-shield"></span> Security Settings</h2>

                                <div class="qu-security-recommendations">
                                    <div
                                        class="qu-security-recommendation <?php echo !empty($encrypted_token) ? 'qu-completed' : 'qu-pending'; ?>">
                                        <div class="qu-recommendation-icon">
                                            <span
                                                class="dashicons <?php echo !empty($encrypted_token) ? 'dashicons-yes-alt' : 'dashicons-warning'; ?>"></span>
                                        </div>
                                        <div class="qu-recommendation-content">
                                            <h4>Configure GitHub Access Token</h4>
                                            <p>A GitHub token improves security by authenticating requests and increases API rate
                                                limits.
                                                Your token is securely encrypted before being stored in the database.
                                            </p>
                                            <a href="<?php echo admin_url('options-general.php?page=q-updater-github-debug'); ?>"
                                                class="qu-button qu-button-small">
                                                <span class="dashicons dashicons-admin-tools"></span> Test GitHub API Connection
                                            </a>
                                        </div>
                                    </div>

                                    <div class="qu-security-recommendation qu-completed">
                                        <div class="qu-recommendation-icon">
                                            <span class="dashicons dashicons-yes-alt"></span>
                                        </div>
                                        <div class="qu-recommendation-content">
                                            <h4>Secure Plugin Installation</h4>
                                            <p>Q-Updater verifies plugin integrity before installation to prevent security issues.</p>
                                        </div>
                                    </div>

                                    <div class="qu-security-recommendation qu-completed">
                                        <div class="qu-recommendation-icon">
                                            <span class="dashicons dashicons-yes-alt"></span>
                                        </div>
                                        <div class="qu-recommendation-content">
                                            <h4>Permission Checks</h4>
                                            <p>Only users with appropriate permissions can manage plugin updates and settings.</p>
                                        </div>
                                    </div>

                                    <div class="qu-security-recommendation qu-completed">
                                        <div class="qu-recommendation-icon">
                                            <span class="dashicons dashicons-yes-alt"></span>
                                        </div>
                                        <div class="qu-recommendation-content">
                                            <h4>Secure Communication</h4>
                                            <p>All API requests use HTTPS with SSL verification to ensure secure communication with
                                                GitHub.
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                <div class="qu-settings-form-group">
                                    <h3>GitHub Repository Management</h3>
                                    <p>Manage which plugins are updated from GitHub, GitLab, or Bitbucket repositories.</p>

                                    <div class="qu-repo-management-tabs">
                                        <a href="#managed-plugins" class="qu-repo-tab active">Managed Plugins</a>
                                        <a href="#all-plugins" class="qu-repo-tab">All Plugins</a>
                                        <a href="#add-custom-repo" class="qu-repo-tab">Add Custom Repository</a>
                                    </div>

                                    <!-- Managed Plugins Tab -->
                                    <div id="managed-plugins" class="qu-repo-tab-content">
                                        <h4>Currently Managed Plugins</h4>
                                        <p>These plugins are currently configured to be updated from GitHub, GitLab, or Bitbucket.</p>

                                        <?php
                                        // Get installed plugins
                                        $installed_plugins = $plugin_manager->get_installed_q_plugins();
                                        $custom_mappings = get_option($this->parent->get_option_name('custom_repo_mappings'), []);

                                        if (empty($installed_plugins)) {
                                            echo '<p class="qu-no-plugins-message">No plugins are currently managed by Q-Updater.</p>';
                                        } else {
                                            echo '<div class="qu-custom-repo-mappings">';

                                            foreach ($installed_plugins as $slug => $repo) {
                                                $plugin_data = $plugin_manager->get_safe_plugin_data("$slug/$slug.php");
                                                if (!$plugin_data)
                                                    continue;

                                                $custom_repo = isset($custom_mappings[$slug]) ? $custom_mappings[$slug] : '';
                                                $is_custom = !empty($custom_repo);
                                                $display_repo = $is_custom ? $custom_repo : $repo;

                                                // Determine plugin type
                                                $plugin_type = 'GitHub';
                                                $plugin_type_class = 'github';
                                                if (strpos($display_repo, 'gitlab:') === 0) {
                                                    $plugin_type = 'GitLab';
                                                    $plugin_type_class = 'gitlab';
                                                    $display_repo = substr($display_repo, 7); // Remove 'gitlab:' prefix for display
                                                } elseif (strpos($display_repo, 'bitbucket:') === 0) {
                                                    $plugin_type = 'Bitbucket';
                                                    $plugin_type_class = 'bitbucket';
                                                    $display_repo = substr($display_repo, 10); // Remove 'bitbucket:' prefix for display
                                                }

                                                ?>
                                                        <div class="qu-custom-repo-item">
                                                            <div class="qu-custom-repo-info">
                                                                <span class="qu-plugin-name"><?php echo esc_html($plugin_data['Name']); ?></span>
                                                                <span
                                                                    class="qu-repo-type <?php echo esc_attr($plugin_type_class); ?>"><?php echo esc_html($plugin_type); ?></span>
                                                            </div>
                                                            <div class="qu-custom-repo-input">
                                                                <input type="text" name="q_updater_custom_repos[<?php echo esc_attr($slug); ?>]"
                                                                    value="<?php echo esc_attr($display_repo); ?>" class="regular-text"
                                                                    placeholder="username/repository">
                                                                <?php if ($is_custom): ?>
                                                                        <span class="qu-custom-repo-badge">Custom</span>
                                                                <?php elseif (strpos($slug, 'q-') === 0): ?>
                                                                        <span class="qu-custom-repo-badge qu-default-badge">Default</span>
                                                                <?php else: ?>
                                                                        <span class="qu-custom-repo-badge qu-detected-badge">Detected</span>
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                        <?php
                                            }

                                            echo '</div>';
                                        }
                                        ?>
                                        <p class="description">
                                            Enter repository paths in the format "username/repository". For GitLab or Bitbucket, prefix
                                            with
                                            "gitlab:" or "bitbucket:".
                                        </p>
                                    </div>

                                    <!-- All Plugins Tab -->
                                    <div id="all-plugins" class="qu-repo-tab-content" style="display: none;">
                                        <h4>All Installed Plugins</h4>
                                        <p>Add any installed plugin to be managed by Q-Updater.</p>

                                        <?php
                                        // Get all installed plugins
                                        $all_plugins = $plugin_manager->get_all_installed_plugins();

                                        if (empty($all_plugins)) {
                                            echo '<p class="qu-no-plugins-message">No plugins are currently installed.</p>';
                                        } else {
                                            echo '<div class="qu-all-plugins-list">';

                                            // Filter options
                                            echo '<div class="qu-filter-options">';
                                            echo '<label for="plugin-filter">Filter: </label>';
                                            echo '<select id="plugin-filter" class="qu-filter-select">';
                                            echo '<option value="all">All Plugins</option>';
                                            echo '<option value="not-managed">Not Managed</option>';
                                            echo '<option value="github-metadata">With GitHub Metadata</option>';
                                            echo '<option value="q-plugin">Q Plugins</option>';
                                            echo '<option value="github">GitHub Plugins</option>';
                                            echo '<option value="gitlab">GitLab Plugins</option>';
                                            echo '<option value="bitbucket">Bitbucket Plugins</option>';
                                            echo '</select>';
                                            echo '</div>';

                                            echo '<div class="qu-plugins-table-container">';
                                            echo '<table class="qu-plugins-table">';
                                            echo '<thead>';
                                            echo '<tr>';
                                            echo '<th>Plugin</th>';
                                            echo '<th>Type</th>';
                                            echo '<th>Repository</th>';
                                            echo '<th>Actions</th>';
                                            echo '</tr>';
                                            echo '</thead>';
                                            echo '<tbody>';

                                            foreach ($all_plugins as $slug => $plugin) {
                                                $is_managed = $plugin['is_github_managed'];
                                                $has_github_metadata = $plugin['has_github_metadata'];
                                                $plugin_type = $plugin['type'];
                                                $plugin_type_display = ucfirst($plugin_type);

                                                // Prepare repository input value
                                                $repo_value = '';
                                                if ($is_managed) {
                                                    $repo_value = $plugin['github_repo'];
                                                    // Remove prefixes for display
                                                    if (strpos($repo_value, 'gitlab:') === 0) {
                                                        $repo_value = substr($repo_value, 7);
                                                    } elseif (strpos($repo_value, 'bitbucket:') === 0) {
                                                        $repo_value = substr($repo_value, 10);
                                                    }
                                                } elseif ($has_github_metadata) {
                                                    // Try to extract repository from metadata
                                                    if (!empty($plugin['plugin_uri']) && strpos($plugin['plugin_uri'], 'github.com') !== false) {
                                                        preg_match('/github\.com\/([a-zA-Z0-9\-_.]+)\/([a-zA-Z0-9\-_.]+)/', $plugin['plugin_uri'], $matches);
                                                        if (count($matches) === 3) {
                                                            $repo_value = $matches[1] . '/' . $matches[2];
                                                        }
                                                    }
                                                }

                                                $row_class = $is_managed ? 'qu-plugin-managed' : '';
                                                $row_class .= $has_github_metadata && !$is_managed ? ' qu-plugin-github-metadata' : '';
                                                $row_class .= ' qu-plugin-type-' . $plugin_type;

                                                echo '<tr class="' . esc_attr($row_class) . '">';
                                                echo '<td class="qu-plugin-name-cell">';
                                                echo '<span class="qu-plugin-name">' . esc_html($plugin['name']) . '</span>';
                                                echo '<span class="qu-plugin-version">v' . esc_html($plugin['version']) . '</span>';
                                                echo '</td>';
                                                echo '<td class="qu-plugin-type-cell">';
                                                echo '<span class="qu-plugin-type qu-type-' . esc_attr($plugin_type) . '">' . esc_html($plugin_type_display) . '</span>';
                                                echo '</td>';
                                                echo '<td class="qu-plugin-repo-cell">';
                                                echo '<input type="text" name="q_updater_custom_repos[' . esc_attr($slug) . ']" ';
                                                echo 'value="' . esc_attr($repo_value) . '" ';
                                                echo 'class="regular-text qu-repo-input" placeholder="username/repository">';
                                                echo '</td>';
                                                echo '<td class="qu-plugin-actions-cell">';
                                                if ($is_managed) {
                                                    echo '<button type="button" class="qu-button qu-button-small qu-remove-repo" data-slug="' . esc_attr($slug) . '">';
                                                    echo '<span class="dashicons dashicons-no"></span> Remove';
                                                    echo '</button>';
                                                } else {
                                                    echo '<button type="button" class="qu-button qu-button-small qu-add-repo" data-slug="' . esc_attr($slug) . '">';
                                                    echo '<span class="dashicons dashicons-plus"></span> Add';
                                                    echo '</button>';
                                                }
                                                echo '</td>';
                                                echo '</tr>';
                                            }

                                            echo '</tbody>';
                                            echo '</table>';
                                            echo '</div>';
                                            echo '</div>';
                                        }
                                        ?>
                                        <p class="description">
                                            Add any plugin to be managed by Q-Updater by entering its repository path.
                                        </p>
                                    </div>

                                    <!-- Add Custom Repository Tab -->
                                    <div id="add-custom-repo" class="qu-repo-tab-content" style="display: none;">
                                        <h4>Add Custom Repository</h4>
                                        <p>Manually add a repository mapping for any plugin.</p>

                                        <div class="qu-add-custom-repo-form">
                                            <div class="qu-form-row">
                                                <label for="custom-plugin-slug">Plugin Slug:</label>
                                                <input type="text" id="custom-plugin-slug" class="regular-text"
                                                    placeholder="plugin-slug">
                                                <p class="description">The directory name of the plugin (e.g., "my-plugin" for
                                                    wp-content/plugins/my-plugin).</p>
                                            </div>

                                            <div class="qu-form-row">
                                                <label for="custom-repo-type">Repository Type:</label>
                                                <select id="custom-repo-type" class="qu-select">
                                                    <option value="github">GitHub</option>
                                                    <option value="gitlab">GitLab</option>
                                                    <option value="bitbucket">Bitbucket</option>
                                                </select>
                                            </div>

                                            <div class="qu-form-row">
                                                <label for="custom-repo-path">Repository Path:</label>
                                                <input type="text" id="custom-repo-path" class="regular-text"
                                                    placeholder="username/repository">
                                                <p class="description">The path to the repository in the format "username/repository".
                                                </p>
                                            </div>

                                            <div class="qu-form-actions">
                                                <button type="button" id="add-custom-repo-btn" class="qu-button qu-button-primary">
                                                    <span class="dashicons dashicons-plus"></span> Add Repository
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <script>
                                    jQuery(document).ready(function ($) {
                                        // Repository management tabs
                                        $('.qu-repo-tab').on('click', function (e) {
                                            e.preventDefault();
                                            var target = $(this).attr('href');

                                            // Update active tab
                                            $('.qu-repo-tab').removeClass('active');
                                            $(this).addClass('active');

                                            // Show target content
                                            $('.qu-repo-tab-content').hide();
                                            $(target).show();
                                        });

                                        // Plugin filtering
                                        $('#plugin-filter').on('change', function () {
                                            var filter = $(this).val();

                                            if (filter === 'all') {
                                                $('.qu-plugins-table tbody tr').show();
                                            } else {
                                                $('.qu-plugins-table tbody tr').hide();
                                                $('.qu-plugins-table tbody tr.qu-plugin-' + filter).show();
                                            }
                                        });

                                        // Add repository button
                                        $('.qu-add-repo').on('click', function () {
                                            var slug = $(this).data('slug');
                                            var input = $('input[name="q_updater_custom_repos[' + slug + ']"]');

                                            if (input.val().trim() === '') {
                                                alert('Please enter a repository path in the format "username/repository".');
                                                input.focus();
                                                return;
                                            }

                                            // Visual feedback
                                            $(this).closest('tr').addClass('qu-plugin-managed');
                                            $(this).html('<span class="dashicons dashicons-yes"></span> Added');
                                            $(this).removeClass('qu-add-repo').addClass('qu-remove-repo');
                                        });

                                        // Remove repository button
                                        $('.qu-remove-repo').on('click', function () {
                                            var slug = $(this).data('slug');
                                            var input = $('input[name="q_updater_custom_repos[' + slug + ']"]');

                                            // Clear the input
                                            input.val('');

                                            // Visual feedback
                                            $(this).closest('tr').removeClass('qu-plugin-managed');
                                            $(this).html('<span class="dashicons dashicons-plus"></span> Add');
                                            $(this).removeClass('qu-remove-repo').addClass('qu-add-repo');
                                        });

                                        // Add custom repository button
                                        $('#add-custom-repo-btn').on('click', function () {
                                            var slug = $('#custom-plugin-slug').val().trim();
                                            var type = $('#custom-repo-type').val();
                                            var path = $('#custom-repo-path').val().trim();

                                            if (slug === '' || path === '') {
                                                alert('Please enter both a plugin slug and repository path.');
                                                return;
                                            }

                                            // Format the repository path based on type
                                            var formattedPath = path;
                                            if (type === 'gitlab') {
                                                formattedPath = 'gitlab:' + path;
                                            } else if (type === 'bitbucket') {
                                                formattedPath = 'bitbucket:' + path;
                                            }

                                            // Check if an input for this slug already exists
                                            var existingInput = $('input[name="q_updater_custom_repos[' + slug + ']"]');

                                            if (existingInput.length > 0) {
                                                // Update existing input
                                                existingInput.val(formattedPath);

                                                // Show success message
                                                alert('Repository mapping updated for plugin: ' + slug);
                                            } else {
                                                // Create a hidden input to store the mapping
                                                var hiddenInput = $('<input>').attr({
                                                    type: 'hidden',
                                                    name: 'q_updater_custom_repos[' + slug + ']',
                                                    value: formattedPath
                                                });

                                                // Append to the form
                                                $('.qu-settings-form').append(hiddenInput);

                                                // Show success message
                                                alert('Repository mapping added for plugin: ' + slug + '\n\nNote: This plugin must be installed for the mapping to take effect.');
                                            }

                                            // Clear the form
                                            $('#custom-plugin-slug').val('');
                                            $('#custom-repo-path').val('');
                                        });
                                    });
                                </script>
                            </div>

                            <!-- Token Sync Settings Section -->
                            <div id="token-sync-settings" class="qu-settings-section" style="display: none;">
                                <h2><span class="dashicons dashicons-admin-multisite"></span> Token Sync Settings</h2>
                                <p class="description">Configure token synchronization across multiple WordPress sites. This allows you
                                    to
                                    manage GitHub tokens from a single master site.</p>

                                <div class="qu-settings-form-group">
                                    <label for="token-sync-mode">
                                        Token Sync Mode
                                        <span class="qu-settings-tooltip">
                                            <span class="dashicons dashicons-editor-help"></span>
                                            <span class="qu-tooltip-content">
                                                Choose whether this site is a master site that provides tokens to other sites, or a
                                                client
                                                site that receives tokens from a master site.
                                            </span>
                                        </span>
                                    </label>
                                    <select id="token-sync-mode" name="q_updater_token_sync_is_master" class="qu-select">
                                        <option value="0" <?php selected(get_option($this->parent->get_option_name('token_sync_is_master'), false), false); ?>>Client Site (Receives tokens)</option>
                                        <option value="1" <?php selected(get_option($this->parent->get_option_name('token_sync_is_master'), false), true); ?>>Master Site (Provides tokens)</option>
                                    </select>
                                    <p class="description">Select whether this site is a master or client site for token synchronization
                                    </p>
                                </div>

                                <div id="token-sync-client-settings" class="qu-settings-subsection" <?php echo get_option($this->parent->get_option_name('token_sync_is_master'), false) ? 'style="display: none;"' : ''; ?>>
                                    <h3>Client Site Settings</h3>
                                    <div class="qu-settings-form-group">
                                        <label for="token-sync-master-url">
                                            Master Site URL
                                            <span class="qu-settings-tooltip">
                                                <span class="dashicons dashicons-editor-help"></span>
                                                <span class="qu-tooltip-content">
                                                    Enter the URL of the master site that will provide the GitHub token.
                                                </span>
                                            </span>
                                        </label>
                                        <input type="url" id="token-sync-master-url" name="q_updater_token_sync_master_url"
                                            class="regular-text"
                                            value="<?php echo esc_attr(get_option($this->parent->get_option_name('token_sync_master_url'), '')); ?>"
                                            placeholder="https://example.com">
                                        <p class="description">The URL of the master site that provides the GitHub token</p>
                                    </div>

                                    <div class="qu-settings-form-group">
                                        <label for="token-sync-key">
                                            Sync Key
                                            <span class="qu-settings-tooltip">
                                                <span class="dashicons dashicons-editor-help"></span>
                                                <span class="qu-tooltip-content">
                                                    Enter the sync key provided by the master site. This is used to authenticate token
                                                    requests.
                                                </span>
                                            </span>
                                        </label>
                                        <input type="text" id="token-sync-key" name="q_updater_token_sync_key" class="regular-text"
                                            value="<?php echo esc_attr(get_option($this->parent->get_option_name('token_sync_key'), '')); ?>"
                                            placeholder="Enter sync key from master site">
                                        <p class="description">The sync key provided by the master site for authentication</p>
                                    </div>

                                    <div class="qu-form-actions">
                                        <button type="button" id="register-with-master" class="qu-button qu-button-secondary">
                                            <span class="dashicons dashicons-admin-network"></span> Register with Master Site
                                        </button>
                                        <button type="button" id="check-token-updates" class="qu-button qu-button-secondary">
                                            <span class="dashicons dashicons-update"></span> Check for Token Updates
                                        </button>
                                    </div>
                                    <div id="token-sync-client-status" class="qu-status-message" style="display: none;"></div>
                                </div>

                                <div id="token-sync-master-settings" class="qu-settings-subsection" <?php echo get_option($this->parent->get_option_name('token_sync_is_master'), false) ? '' : 'style="display: none;"'; ?>>
                                    <h3>Master Site Settings</h3>
                                    <div class="qu-settings-form-group">
                                        <label for="token-sync-key-master">
                                            Sync Key
                                            <span class="qu-settings-tooltip">
                                                <span class="dashicons dashicons-editor-help"></span>
                                                <span class="qu-tooltip-content">
                                                    This is the sync key that client sites will use to authenticate with this master
                                                    site.
                                                </span>
                                            </span>
                                        </label>
                                        <?php $sync_key = get_option($this->parent->get_option_name('token_sync_key'), ''); ?>
                                        <div class="qu-copy-field">
                                            <input type="text" id="token-sync-key-master" class="regular-text"
                                                value="<?php echo esc_attr($sync_key); ?>" readonly>
                                            <button type="button" class="qu-copy-button" data-clipboard-target="#token-sync-key-master">
                                                <span class="dashicons dashicons-clipboard"></span>
                                            </button>
                                        </div>
                                        <p class="description">Share this key with client sites to allow them to authenticate</p>
                                    </div>

                                    <div class="qu-settings-form-group">
                                        <label>
                                            Connected Sites
                                            <span class="qu-settings-tooltip">
                                                <span class="dashicons dashicons-editor-help"></span>
                                                <span class="qu-tooltip-content">
                                                    These are the sites that are currently connected to this master site.
                                                </span>
                                            </span>
                                        </label>
                                        <?php $connected_sites = get_option($this->parent->get_option_name('token_sync_connected_sites'), []); ?>
                                        <?php if (empty($connected_sites)): ?>
                                                <p class="qu-empty-message">No sites connected yet.</p>
                                        <?php else: ?>
                                                <ul class="qu-connected-sites-list">
                                                    <?php foreach ($connected_sites as $site_url): ?>
                                                            <li>
                                                                <span class="dashicons dashicons-admin-site"></span>
                                                                <?php echo esc_html($site_url); ?>
                                                                <button type="button" class="qu-remove-site"
                                                                    data-site="<?php echo esc_attr($site_url); ?>">
                                                                    <span class="dashicons dashicons-no"></span>
                                                                </button>
                                                            </li>
                                                    <?php endforeach; ?>
                                                </ul>
                                        <?php endif; ?>
                                    </div>

                                    <div class="qu-form-actions">
                                        <button type="button" id="generate-new-key" class="qu-button qu-button-secondary">
                                            <span class="dashicons dashicons-privacy"></span> Generate New Sync Key
                                        </button>
                                        <button type="button" id="push-token-updates" class="qu-button qu-button-secondary">
                                            <span class="dashicons dashicons-update"></span> Push Token Updates
                                        </button>
                                    </div>
                                    <div id="token-sync-master-status" class="qu-status-message" style="display: none;"></div>
                                </div>

                                <script>
                                    jQuery(document).ready(function ($) {
                                        // Toggle between master and client settings
                                        $('#token-sync-mode').on('change', function () {
                                            var ismaster = $(this).val() === '1';
                                            if (ismaster) {
                                                $('#token-sync-client-settings').hide();
                                                $('#token-sync-master-settings').show();
                                            } else {
                                                $('#token-sync-client-settings').show();
                                                $('#token-sync-master-settings').hide();
                                            }
                                        });

                                        // Copy sync key to clipboard
                                        $('.qu-copy-button').on('click', function () {
                                            var targetId = $(this).data('clipboard-target');
                                            var $target = $(targetId);

                                            $target.select();
                                            document.execCommand('copy');

                                            // Show feedback
                                            var $button = $(this);
                                            var originalHtml = $button.html();
                                            $button.html('<span class="dashicons dashicons-yes"></span>');

                                            setTimeout(function () {
                                                $button.html(originalHtml);
                                            }, 1000);
                                        });

                                        // Register with master site
                                        $('#register-with-master').on('click', function () {
                                            var $button = $(this);
                                            var $status = $('#token-sync-client-status');

                                            $button.prop('disabled', true).html('<span class="dashicons dashicons-update qu-spin"></span> Registering...');
                                            $status.removeClass('qu-success qu-error').hide();

                                            $.ajax({
                                                url: ajaxurl,
                                                type: 'POST',
                                                data: {
                                                    action: 'qu_register_with_master',
                                                    nonce: '<?php echo wp_create_nonce('qu_token_sync_nonce'); ?>'
                                                },
                                                success: function (response) {
                                                    if (response.success) {
                                                        $status.addClass('qu-success').html('<span class="dashicons dashicons-yes"></span> ' + response.data.message).show();
                                                    } else {
                                                        $status.addClass('qu-error').html('<span class="dashicons dashicons-no"></span> ' + response.data.message).show();
                                                    }
                                                },
                                                error: function () {
                                                    $status.addClass('qu-error').html('<span class="dashicons dashicons-no"></span> Connection error. Please try again.').show();
                                                },
                                                complete: function () {
                                                    $button.prop('disabled', false).html('<span class="dashicons dashicons-admin-network"></span> Register with Master Site');
                                                }
                                            });
                                        });

                                        // Check for token updates
                                        $('#check-token-updates').on('click', function () {
                                            var $button = $(this);
                                            var $status = $('#token-sync-client-status');

                                            $button.prop('disabled', true).html('<span class="dashicons dashicons-update qu-spin"></span> Checking...');
                                            $status.removeClass('qu-success qu-error').hide();

                                            $.ajax({
                                                url: ajaxurl,
                                                type: 'POST',
                                                data: {
                                                    action: 'qu_check_token_updates',
                                                    nonce: '<?php echo wp_create_nonce('qu_token_sync_nonce'); ?>'
                                                },
                                                success: function (response) {
                                                    if (response.success) {
                                                        $status.addClass('qu-success').html('<span class="dashicons dashicons-yes"></span> ' + response.data.message).show();
                                                    } else {
                                                        $status.addClass('qu-error').html('<span class="dashicons dashicons-no"></span> ' + response.data.message).show();
                                                    }
                                                },
                                                error: function () {
                                                    $status.addClass('qu-error').html('<span class="dashicons dashicons-no"></span> Connection error. Please try again.').show();
                                                },
                                                complete: function () {
                                                    $button.prop('disabled', false).html('<span class="dashicons dashicons-update"></span> Check for Token Updates');
                                                }
                                            });
                                        });

                                        // Generate new sync key
                                        $('#generate-new-key').on('click', function () {
                                            if (!confirm('Are you sure you want to generate a new sync key? All client sites will need to be updated with the new key.')) {
                                                return;
                                            }

                                            var $button = $(this);
                                            var $status = $('#token-sync-master-status');

                                            $button.prop('disabled', true).html('<span class="dashicons dashicons-update qu-spin"></span> Generating...');
                                            $status.removeClass('qu-success qu-error').hide();

                                            $.ajax({
                                                url: ajaxurl,
                                                type: 'POST',
                                                data: {
                                                    action: 'qu_generate_new_sync_key',
                                                    nonce: '<?php echo wp_create_nonce('qu_token_sync_nonce'); ?>'
                                                },
                                                success: function (response) {
                                                    if (response.success) {
                                                        $('#token-sync-key-master').val(response.data.key);
                                                        $status.addClass('qu-success').html('<span class="dashicons dashicons-yes"></span> ' + response.data.message).show();
                                                    } else {
                                                        $status.addClass('qu-error').html('<span class="dashicons dashicons-no"></span> ' + response.data.message).show();
                                                    }
                                                },
                                                error: function () {
                                                    $status.addClass('qu-error').html('<span class="dashicons dashicons-no"></span> Connection error. Please try again.').show();
                                                },
                                                complete: function () {
                                                    $button.prop('disabled', false).html('<span class="dashicons dashicons-privacy"></span> Generate New Sync Key');
                                                }
                                            });
                                        });

                                        // Push token updates
                                        $('#push-token-updates').on('click', function () {
                                            var $button = $(this);
                                            var $status = $('#token-sync-master-status');

                                            $button.prop('disabled', true).html('<span class="dashicons dashicons-update qu-spin"></span> Pushing updates...');
                                            $status.removeClass('qu-success qu-error').hide();

                                            $.ajax({
                                                url: ajaxurl,
                                                type: 'POST',
                                                data: {
                                                    action: 'qu_push_token_updates',
                                                    nonce: '<?php echo wp_create_nonce('qu_token_sync_nonce'); ?>'
                                                },
                                                success: function (response) {
                                                    if (response.success) {
                                                        $status.addClass('qu-success').html('<span class="dashicons dashicons-yes"></span> ' + response.data.message).show();
                                                    } else {
                                                        $status.addClass('qu-error').html('<span class="dashicons dashicons-no"></span> ' + response.data.message).show();
                                                    }
                                                },
                                                error: function () {
                                                    $status.addClass('qu-error').html('<span class="dashicons dashicons-no"></span> Connection error. Please try again.').show();
                                                },
                                                complete: function () {
                                                    $button.prop('disabled', false).html('<span class="dashicons dashicons-update"></span> Push Token Updates');
                                                }
                                            });
                                        });

                                        // Remove connected site
                                        $('.qu-remove-site').on('click', function () {
                                            var site = $(this).data('site');
                                            if (!confirm('Are you sure you want to remove this site? It will no longer receive token updates.')) {
                                                return;
                                            }

                                            var $button = $(this);
                                            var $status = $('#token-sync-master-status');

                                            $button.prop('disabled', true);
                                            $status.removeClass('qu-success qu-error').hide();

                                            $.ajax({
                                                url: ajaxurl,
                                                type: 'POST',
                                                data: {
                                                    action: 'qu_remove_connected_site',
                                                    nonce: '<?php echo wp_create_nonce('qu_token_sync_nonce'); ?>',
                                                    site: site
                                                },
                                                success: function (response) {
                                                    if (response.success) {
                                                        $button.closest('li').fadeOut(function () {
                                                            $(this).remove();
                                                            if ($('.qu-connected-sites-list li').length === 0) {
                                                                $('.qu-connected-sites-list').replaceWith('<p class="qu-empty-message">No sites connected yet.</p>');
                                                            }
                                                        });
                                                        $status.addClass('qu-success').html('<span class="dashicons dashicons-yes"></span> ' + response.data.message).show();
                                                    } else {
                                                        $status.addClass('qu-error').html('<span class="dashicons dashicons-no"></span> ' + response.data.message).show();
                                                        $button.prop('disabled', false);
                                                    }
                                                },
                                                error: function () {
                                                    $status.addClass('qu-error').html('<span class="dashicons dashicons-no"></span> Connection error. Please try again.').show();
                                                    $button.prop('disabled', false);
                                                }
                                            });
                                        });
                                    });
                                </script>
                            </div>

                            <div class="qu-settings-form-actions">
                                <?php submit_button('Save Settings', 'primary', 'submit', true, ['class' => 'qu-button-large']); ?>
                            </div>
                        </form>
                    </div>
                </div>
                <?php
    }

    /**
     * Render the Plugin Manager tab
     *
     * @param Q_Updater_Plugin_Manager $plugin_manager Plugin manager instance
     */
    private function render_plugin_manager_tab($plugin_manager)
    {
        // Get installed plugins
        $installed_plugins = $plugin_manager->get_installed_q_plugins();
        $github_api = new Q_Updater_GitHub_API($this->parent);
        ?>
                <div class="qu-plugin-manager-section">
                    <!-- Plugin Manager Navigation -->
                    <div class="qu-plugin-manager-nav">
                        <a href="#installed-plugins" class="qu-plugin-manager-nav-item active">
                            Installed Plugins
                        </a>
                        <a href="#install-new" class="qu-plugin-manager-nav-item">
                            Install New
                        </a>
                        <a href="#browse-github" class="qu-plugin-manager-nav-item">
                            Browse GitHub
                        </a>
                    </div>

                    <!-- Installed Plugins Section -->
                    <div id="installed-plugins" class="qu-plugin-manager-content">
                        <div class="qu-settings-card">
                            <div class="qu-card-header">
                                <h2><span class="dashicons dashicons-plugins-checked"></span> Installed Plugins</h2>
                                <div class="qu-button-group">
                                    <button type="button" id="check-all-updates" class="qu-button qu-button-primary"
                                        aria-label="Check for updates for all plugins">
                                        <span class="dashicons dashicons-update" aria-hidden="true"></span> Check for Updates
                                    </button>
                                    <button type="button" id="run-all-updates" class="qu-button qu-button-secondary"
                                        aria-label="Update all plugins to their latest versions">
                                        <span class="dashicons dashicons-update-alt" aria-hidden="true"></span> Update All
                                    </button>
                                </div>
                            </div>

                            <div id="manual-check-status" class="qu-status-message" style="display: none;"></div>

                            <?php if (empty($installed_plugins)): ?>
                                    <div class="qu-empty-state">
                                        <span class="dashicons dashicons-plugins-checked" aria-hidden="true"></span>
                                        <p>No Q plugins installed yet</p>
                                        <a href="#install-new" class="qu-button qu-button-primary qu-nav-button qu-pulse">
                                            <span class="dashicons dashicons-download" aria-hidden="true"></span> Install Your First Plugin
                                        </a>
                                    </div>
                            <?php else: ?>
                                    <div class="qu-plugins-grid">
                                        <?php foreach ($installed_plugins as $slug => $repo):
                                            $version_info = $plugin_manager->get_plugin_version_info($slug, $repo);
                                            if (!$version_info)
                                                continue;

                                            $has_error = isset($version_info['error']);
                                            $status_class = $has_error ? 'qu-error' : ($version_info['has_update'] ? 'qu-update-available' : 'qu-up-to-date');
                                            $status_text = $has_error ? __('Error checking updates', 'q-updater') :
                                                ($version_info['has_update'] ? __('Update Available', 'q-updater') : __('Up to Date', 'q-updater'));
                                            $is_active = is_plugin_active("$slug/$slug.php");
                                            $plugin_status_class = $is_active ? 'qu-status-active' : 'qu-status-inactive';
                                            $plugin_status_text = $is_active ? __('Active', 'q-updater') : __('Inactive', 'q-updater');

                                            // Determine if update button should be highlighted
                                            $update_button_class = $version_info['has_update'] ? 'qu-button-primary qu-pulse' : 'qu-button-secondary';
                                            $update_button_text = $version_info['has_update'] ? __('Update Now', 'q-updater') : __('Re-install', 'q-updater');
                                            $update_button_disabled = $has_error ? 'disabled' : '';
                                            ?>
                                                <div class="qu-plugin-card <?php echo $version_info['has_update'] ? 'qu-card-highlight' : ''; ?>">
                                                    <!-- Status indicator -->
                                                    <div
                                                        class="qu-plugin-status-indicator <?php echo $has_error ? 'qu-status-error' : ($version_info['has_update'] ? 'qu-status-update-available' : 'qu-status-up-to-date'); ?>">
                                                        <?php if ($version_info['has_update']): ?>
                                                                <span class="dashicons dashicons-warning" aria-hidden="true"></span>
                                                        <?php elseif (!$has_error): ?>
                                                                <span class="dashicons dashicons-yes-alt" aria-hidden="true"></span>
                                                        <?php else: ?>
                                                                <span class="dashicons dashicons-warning" aria-hidden="true"></span>
                                                        <?php endif; ?>
                                                    </div>

                                                    <div class="qu-plugin-header">
                                                        <h3 class="qu-plugin-title"><?php echo esc_html($version_info['name']); ?></h3>
                                                        <div class="qu-plugin-status-badges">
                                                            <span class="qu-version-status <?php echo esc_attr($status_class); ?>">
                                                                <?php if ($version_info['has_update']): ?>
                                                                        <span class="dashicons dashicons-warning" aria-hidden="true"></span>
                                                                <?php elseif (!$has_error): ?>
                                                                        <span class="dashicons dashicons-yes-alt" aria-hidden="true"></span>
                                                                <?php else: ?>
                                                                        <span class="dashicons dashicons-warning" aria-hidden="true"></span>
                                                                <?php endif; ?>
                                                                <?php echo esc_html($status_text); ?>
                                                            </span>
                                                            <span class="qu-plugin-status <?php echo esc_attr($plugin_status_class); ?>">
                                                                <?php if ($is_active): ?>
                                                                        <span class="dashicons dashicons-yes" aria-hidden="true"></span>
                                                                <?php else: ?>
                                                                        <span class="dashicons dashicons-marker" aria-hidden="true"></span>
                                                                <?php endif; ?>
                                                                <?php echo esc_html($plugin_status_text); ?>
                                                            </span>
                                                        </div>
                                                    </div>

                                                    <div class="qu-plugin-meta">

                                                        <div class="qu-plugin-versions">
                                                            <div class="qu-version-item">
                                                                <span class="qu-version-label">Current:</span>
                                                                <span
                                                                    class="qu-version-number"><?php echo esc_html($version_info['current_version']); ?></span>
                                                            </div>
                                                            <?php if ($version_info['latest_version']): ?>
                                                                    <div
                                                                        class="qu-version-item <?php echo $version_info['has_update'] ? 'qu-version-newer' : ''; ?>">
                                                                        <span class="qu-version-label">Latest:</span>
                                                                        <span class="qu-version-number">
                                                                            <?php if ($version_info['has_update']): ?>
                                                                                    <span class="dashicons dashicons-arrow-up-alt" aria-hidden="true"></span>
                                                                            <?php endif; ?>
                                                                            <?php echo esc_html($version_info['latest_version']); ?>
                                                                        </span>
                                                                    </div>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>

                                                    <div class="qu-plugin-actions">
                                                        <button type="button"
                                                            class="qu-plugin-action-btn <?php echo $version_info['has_update'] ? 'qu-btn-primary' : 'qu-btn-success'; ?> qu-update-plugin"
                                                            data-plugin="<?php echo esc_attr($slug); ?>"
                                                            data-version="<?php echo esc_attr($version_info['latest_version'] ?: 'latest'); ?>" <?php echo $update_button_disabled; ?>
                                                            aria-label="<?php echo esc_attr($update_button_text . ' ' . $version_info['name']); ?>">
                                                            <span class="dashicons dashicons-update" aria-hidden="true"></span>
                                                            <?php echo esc_html($update_button_text); ?>
                                                        </button>

                                                        <div class="qu-dropdown">
                                                            <button type="button" class="qu-plugin-action-btn qu-dropdown-toggle" aria-haspopup="true"
                                                                aria-expanded="false">
                                                                <span class="dashicons dashicons-admin-tools" aria-hidden="true"></span> Actions
                                                                <span class="dashicons dashicons-arrow-down-alt2" aria-hidden="true"></span>
                                                            </button>
                                                            <div class="qu-dropdown-menu">
                                                                <button type="button" class="qu-dropdown-item qu-rollback-button"
                                                                    data-plugin="<?php echo esc_attr($slug); ?>"
                                                                    data-version="<?php echo esc_attr($version_info['current_version']); ?>"
                                                                    data-url="<?php echo esc_url($github_api->get_github_download_url($repo, $version_info['current_version'])); ?>"
                                                                    aria-label="Rollback <?php echo esc_attr($version_info['name']); ?>">
                                                                    <span class="dashicons dashicons-backup" aria-hidden="true"></span> Rollback
                                                                </button>

                                                                <button type="button" class="qu-dropdown-item qu-uninstall-plugin qu-btn-danger"
                                                                    data-plugin="<?php echo esc_attr($slug); ?>"
                                                                    aria-label="Uninstall <?php echo esc_attr($version_info['name']); ?>">
                                                                    <span class="dashicons dashicons-trash" aria-hidden="true"></span> Uninstall
                                                                </button>

                                                            </div>
                                                        </div>
                                                    </div>

                                                    <?php if ($has_error): ?>
                                                            <div class="qu-plugin-error-message">
                                                                <span class="dashicons dashicons-warning" aria-hidden="true"></span>
                                                                <?php echo esc_html($version_info['error']); ?>
                                                            </div>
                                                    <?php endif; ?>
                                                </div>
                                        <?php endforeach; ?>
                                    </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Install New Plugin Section -->
                    <div id="install-new" class="qu-plugin-manager-content" style="display: none;">
                        <div class="qu-settings-card">
                            <h2><span class="dashicons dashicons-download"></span> Install New Plugin from Repository</h2>

                            <div class="qu-group">
                                <div class="qu-settings-form-group">
                                    <label for="repo-type-install">
                                        Repository Type
                                        <span class="qu-settings-tooltip">
                                            <span class="dashicons dashicons-editor-help"></span>
                                            <span class="qu-tooltip-content">
                                                Select the type of repository where your plugin is hosted.
                                            </span>
                                        </span>
                                    </label>
                                    <select id="repo-type-install" class="qu-select">
                                        <option value="github" selected>GitHub</option>
                                        <option value="gitlab">GitLab</option>
                                        <option value="bitbucket">Bitbucket</option>
                                    </select>
                                    <p class="description">The type of repository where your plugin is hosted</p>
                                </div>

                                <div class="qu-settings-form-group">
                                    <label for="github-repo">
                                        Repository Path
                                        <span class="qu-settings-tooltip">
                                            <span class="dashicons dashicons-editor-help"></span>
                                            <span class="qu-tooltip-content">
                                                Enter the repository path in the format "username/repository".
                                                For example, "shamielo/q-example".
                                            </span>
                                        </span>
                                    </label>
                                    <input type="text" id="github-repo" class="regular-text"
                                        placeholder="username/repository (e.g., shamielo/q-example)">
                                    <p class="description">The repository path for the plugin you want to install</p>
                                </div>

                                <div class="qu-settings-form-group">
                                    <label for="github-branch">
                                        Branch or Tag (Optional)
                                        <span class="qu-settings-tooltip">
                                            <span class="dashicons dashicons-editor-help"></span>
                                            <span class="qu-tooltip-content">
                                                Specify a branch or tag to install a specific version.
                                                Leave empty to install from the default branch (usually "main" or "master").
                                            </span>
                                        </span>
                                    </label>
                                    <input type="text" id="github-branch" class="regular-text"
                                        placeholder="Branch/Tag (e.g., main, v1.0.0)">
                                    <p class="description">Specify a branch or tag to install a specific version</p>
                                </div>

                            </div>

                            <div class="qu-form-actions">
                                <button type="button" id="install-github-plugin"
                                    class="qu-plugin-action-btn qu-btn-primary qu-button-large">
                                    <span class="dashicons dashicons-download"></span> Install Plugin
                                </button>
                            </div>

                            <div id="install-plugin-status" class="qu-install-status"></div>
                        </div>
                    </div>

                    <!-- Browse Repository Plugins Section -->
                    <div id="browse-github" class="qu-plugin-manager-content" style="display: none;">
                        <div class="qu-settings-card">
                            <h2><span class="dashicons dashicons-search"></span> Browse Repository Plugins</h2>

                            <div class="qu-search-form">
                                <div class="qu-settings-form-group">
                                    <label for="github-search">
                                        Search for WordPress Plugins
                                        <span class="qu-settings-tooltip">
                                            <span class="dashicons dashicons-editor-help"></span>
                                            <span class="qu-tooltip-content">
                                                Search for WordPress plugins across GitHub, GitLab, and Bitbucket repositories. You can
                                                use keywords like "woocommerce", "seo", etc.
                                            </span>
                                        </span>
                                    </label>
                                    <div class="qu-search-input-group">
                                        <input type="text" id="github-search" class="regular-text" placeholder="Search for plugins...">
                                        <button type="button" id="search-github-plugins" class="qu-plugin-action-btn qu-btn-primary">
                                            <span class="dashicons dashicons-search"></span> Search
                                        </button>
                                    </div>
                                </div>

                                <div class="qu-search-filters">
                                    <div class="qu-filter-group">
                                        <label for="repo-type">Repository:</label>
                                        <select id="repo-type" class="qu-select">
                                            <option value="all">All Repositories</option>
                                            <option value="github" selected>GitHub</option>
                                            <option value="gitlab">GitLab</option>
                                            <option value="bitbucket">Bitbucket</option>
                                        </select>
                                    </div>

                                    <div class="qu-filter-group">
                                        <label for="github-sort">Sort by:</label>
                                        <select id="github-sort" class="qu-select">
                                            <option value="stars">Stars</option>
                                            <option value="updated">Last Updated</option>
                                            <option value="forks">Forks</option>
                                        </select>
                                    </div>

                                    <div class="qu-filter-group">
                                        <label for="github-order">Order:</label>
                                        <select id="github-order" class="qu-select">
                                            <option value="desc">Descending</option>
                                            <option value="asc">Ascending</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div id="github-search-results" class="qu-search-results">
                                <div class="qu-empty-state">
                                    <span class="dashicons dashicons-search"></span>
                                    <p>Search for plugins to see results</p>
                                </div>
                            </div>

                            <div id="github-search-status" class="qu-search-status"></div>
                        </div>
                    </div>
                </div>
                <?php
    }

    /**
     * Render the Reviews tab
     *
     * @param Q_Updater_Plugin_Manager $plugin_manager Plugin manager instance
     */
    private function render_reviews_tab($plugin_manager)
    {
        // Get the reviews instance
        $reviews = $this->parent->get_reviews();

        // Get developer email directly from the option
        $developer_email = get_option('q_updater_developer_email', '');

        // Get installed plugins
        $installed_plugins = $plugin_manager->get_installed_q_plugins();

        // Get selected plugin for filtering
        $selected_plugin = isset($_GET['plugin']) ? sanitize_text_field($_GET['plugin']) : '';

        // Get reviews for the selected plugin or all reviews
        if (!empty($selected_plugin) && isset($installed_plugins[$selected_plugin])) {
            $plugin_reviews = $reviews->get_plugin_reviews($selected_plugin);
            $review_count = count($plugin_reviews);
            $average_rating = $reviews->get_average_rating($selected_plugin);

            // Get plugin data
            $plugin_data = $plugin_manager->get_safe_plugin_data("$selected_plugin/$selected_plugin.php");
            $plugin_name = $plugin_data ? $plugin_data['Name'] : $selected_plugin;
        } else {
            $plugin_reviews = $reviews->get_all_reviews(20); // Limit to 20 most recent reviews
            $review_count = count($plugin_reviews);
            $average_rating = 0;
            $plugin_name = 'All Plugins';
            $selected_plugin = '';
        }
        ?>
                <div class="qu-reviews-section">
                    <!-- Reviews Header -->
                    <div class="qu-settings-card">
                        <div class="qu-card-header">
                            <h2><span class="dashicons dashicons-star-filled"></span> Plugin Reviews</h2>
                            <div class="qu-button-group">
                                <a href="#write-review" class="qu-plugin-action-btn qu-btn-primary qu-write-review-button">
                                    <span class="dashicons dashicons-edit"></span> Write a Review
                                </a>
                            </div>
                        </div>

                        <div class="qu-group">

                            <!-- Plugin Filter -->
                            <div class="qu-reviews-filter">
                                <form method="get" action="">
                                    <input type="hidden" name="page" value="q-updater">
                                    <input type="hidden" name="tab" value="reviews">

                                    <div class="qu-filter-row">
                                        <div class="qu-filter-group">
                                            <label for="plugin-filter">Filter by Plugin:</label>
                                            <select name="plugin" id="plugin-filter" class="qu-select">
                                                <option value="">All Plugins</option>
                                                <?php foreach ($installed_plugins as $slug => $repo):
                                                    $plugin_data = $plugin_manager->get_safe_plugin_data("$slug/$slug.php");
                                                    if (!$plugin_data)
                                                        continue;
                                                    $selected = $selected_plugin === $slug ? 'selected' : '';
                                                    ?>
                                                        <option value="<?php echo esc_attr($slug); ?>" <?php echo $selected; ?>>
                                                            <?php echo esc_html($plugin_data['Name']); ?>
                                                        </option>
                                                <?php endforeach; ?>
                                            </select>
                                        </div>

                                        <div class="qu-filter-actions">
                                            <button type="submit" class="qu-plugin-action-btn">
                                                <span class="dashicons dashicons-filter"></span> Filter
                                            </button>

                                            <?php if (!empty($selected_plugin)): ?>
                                                    <a href="?page=q-updater&tab=reviews" class="qu-button qu-button-text">
                                                        <span class="dashicons dashicons-dismiss"></span> Clear Filter
                                                    </a>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </form>
                            </div>

                            <!-- Reviews Summary -->
                            <div class="qu-reviews-summary">
                                <div class="qu-reviews-stats">
                                    <div class="qu-reviews-count">
                                        <span class="qu-count-number"><?php echo esc_html($review_count); ?></span>
                                        <span
                                            class="qu-count-label"><?php echo _n('Review', 'Reviews', $review_count, 'q-updater'); ?></span>
                                    </div>

                                    <?php if (!empty($selected_plugin) && $review_count > 0): ?>
                                            <div class="qu-reviews-average">
                                                <div class="qu-average-rating">
                                                    <?php echo $reviews->render_star_rating($average_rating); ?>
                                                    <span
                                                        class="qu-rating-number"><?php echo esc_html(number_format($average_rating, 1)); ?></span>
                                                </div>
                                                <span class="qu-average-label">Average Rating</span>
                                            </div>
                                    <?php endif; ?>
                                </div>

                                <div class="qu-reviews-plugin-info">
                                    <h3><?php echo esc_html($plugin_name); ?></h3>
                                    <?php if (!empty($selected_plugin)): ?>
                                            <div class="qu-plugin-meta">
                                                <span
                                                    class="qu-plugin-repo"><?php echo esc_html($installed_plugins[$selected_plugin]); ?></span>
                                                <?php if ($plugin_data): ?>
                                                        <span class="qu-plugin-version">v<?php echo esc_html($plugin_data['Version']); ?></span>
                                                <?php endif; ?>
                                            </div>
                                    <?php endif; ?>
                                </div>
                            </div>


                        </div>
                    </div>

                    <!-- Write Review Form -->
                    <div id="write-review" class="qu-settings-card qu-write-review-form" style="display: none;">
                        <div class="qu-card-header">
                            <h2><span class="dashicons dashicons-edit"></span> Write a Review</h2>
                        </div>

                        <form id="qu-review-form" method="post">
                            <div class="qu-settings-form-group">
                                <label for="review-plugin">Select Plugin:</label>
                                <select name="plugin_slug" id="review-plugin" class="qu-select" required>
                                    <option value="">-- Select a plugin --</option>
                                    <?php foreach ($installed_plugins as $slug => $repo):
                                        $plugin_data = $plugin_manager->get_safe_plugin_data("$slug/$slug.php");
                                        if (!$plugin_data)
                                            continue;
                                        $selected = $selected_plugin === $slug ? 'selected' : '';
                                        ?>
                                            <option value="<?php echo esc_attr($slug); ?>" <?php echo $selected; ?>>
                                                <?php echo esc_html($plugin_data['Name']); ?>
                                            </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div class="qu-settings-form-group">
                                <label for="review-rating">Rating:</label>
                                <div class="qu-rating-input">
                                    <?php echo $reviews->render_star_rating(0, true); ?>
                                    <input type="hidden" name="rating" id="review-rating" value="0" required>
                                </div>
                            </div>

                            <div class="qu-settings-form-group">
                                <label for="review-text">Your Review:</label>
                                <textarea name="review_text" id="review-text" rows="5" required></textarea>
                                <p class="description">
                                    Please share your experience with this plugin. What did you like? What could be improved?
                                </p>
                            </div>

                            <div class="qu-form-actions">
                                <button type="submit" class="qu-plugin-action-btn qu-btn-primary">
                                    <span class="dashicons dashicons-saved"></span> Submit Review
                                </button>
                                <button type="button" class="qu-plugin-action-btn qu-cancel-review">
                                    <span class="dashicons dashicons-no-alt"></span> Cancel
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Reviews List -->
                    <div class="qu-settings-card">
                        <div class="qu-card-header">
                            <h2>
                                <?php if (!empty($selected_plugin)): ?>
                                        Reviews for <?php echo esc_html($plugin_name); ?>
                                <?php else: ?>
                                        Recent Reviews
                                <?php endif; ?>
                            </h2>
                        </div>

                        <?php if (empty($plugin_reviews)): ?>
                                <div class="qu-empty-state">
                                    <span class="dashicons dashicons-star-empty"></span>
                                    <p>No reviews found</p>
                                    <a href="#write-review" class="qu-plugin-action-btn qu-btn-primary qu-write-review-button">
                                        <span class="dashicons dashicons-edit"></span> Be the First to Write a Review
                                    </a>
                                </div>
                        <?php else: ?>
                                <div class="qu-reviews-list">
                                    <?php foreach ($plugin_reviews as $review):
                                        // Get plugin data
                                        if (empty($selected_plugin)) {
                                            $review_plugin_data = $plugin_manager->get_safe_plugin_data("{$review->plugin_slug}/{$review->plugin_slug}.php");
                                            $review_plugin_name = $review_plugin_data ? $review_plugin_data['Name'] : $review->plugin_slug;
                                        }
                                        ?>
                                            <div class="qu-review-item" data-review-id="<?php echo esc_attr($review->id); ?>">
                                                <div class="qu-review-header">
                                                    <div class="qu-review-meta">
                                                        <div class="qu-reviewer-name"><?php echo esc_html($review->user_name); ?></div>
                                                        <div class="qu-review-date">
                                                            <?php echo esc_html(human_time_diff(strtotime($review->review_date), current_time('timestamp'))); ?>
                                                            ago
                                                        </div>
                                                    </div>
                                                    <div class="qu-review-rating">
                                                        <?php echo $reviews->render_star_rating($review->rating); ?>
                                                    </div>
                                                </div>

                                                <?php if (empty($selected_plugin)): ?>
                                                        <div class="qu-review-plugin">
                                                            <span class="dashicons dashicons-admin-plugins"></span>
                                                            <?php echo esc_html($review_plugin_name); ?>
                                                        </div>
                                                <?php endif; ?>

                                                <div class="qu-review-content">
                                                    <?php echo wpautop(esc_html($review->review_text)); ?>
                                                </div>

                                                <?php if (current_user_can('manage_options')): ?>
                                                        <div class="qu-review-actions">
                                                            <button type="button" class="qu-plugin-action-btn qu-btn-danger qu-delete-review"
                                                                data-review-id="<?php echo esc_attr($review->id); ?>">
                                                                <span class="dashicons dashicons-trash"></span> Delete
                                                            </button>
                                                        </div>
                                                <?php endif; ?>
                                            </div>
                                    <?php endforeach; ?>
                                </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php
    }

    /**
     * Render the Tools tab
     *
     * @param Q_Updater_Plugin_Manager $plugin_manager Plugin manager instance
     */
    /**
     * Process analytics settings
     */
    public function process_analytics_settings()
    {
        // Get current settings
        $analytics_settings = get_option($this->parent->get_option_name('analytics'), ['enabled' => true, 'weekly_report' => true]);

        // Check if toggle analytics button is clicked
        if (isset($_POST['toggle_analytics'])) {
            // Verify nonce
            check_admin_referer('qu_analytics_settings', 'qu_analytics_nonce');

            // Toggle enabled state
            $analytics_settings['enabled'] = !isset($analytics_settings['enabled']) || !$analytics_settings['enabled'];

            // Save settings
            update_option($this->parent->get_option_name('analytics'), $analytics_settings);

            // Add success message
            add_settings_error(
                'q_updater_analytics',
                'analytics_updated',
                $analytics_settings['enabled']
                ? __('Analytics enabled successfully.', 'q-updater')
                : __('Analytics disabled successfully.', 'q-updater'),
                'updated'
            );
        }

        // Check if update analytics settings button is clicked
        if (isset($_POST['update_analytics_settings'])) {
            // Verify nonce
            check_admin_referer('qu_analytics_settings', 'qu_analytics_nonce');

            // Update weekly report setting
            $analytics_settings['weekly_report'] = isset($_POST['weekly_report']) ? true : false;

            // Save settings
            update_option($this->parent->get_option_name('analytics'), $analytics_settings);

            // Add success message
            add_settings_error(
                'q_updater_analytics',
                'analytics_settings_updated',
                __('Analytics settings updated successfully.', 'q-updater'),
                'updated'
            );

            // If weekly reports are enabled, make sure the cron job is scheduled
            $analytics = $this->parent->get_analytics();
            $analytics->schedule_weekly_report();

            // Log for debugging
            if ($analytics_settings['weekly_report']) {
                error_log('Weekly analytics report scheduled after settings update');
            } else {
                error_log('Weekly analytics report schedule cleared');
            }
        }

        // Check if send test report button is clicked
        if (isset($_POST['send_test_report'])) {
            // Debug logging
            error_log('Q-Updater: Test report button clicked');
            error_log('Q-Updater: POST data: ' . print_r($_POST, true));

            // Check if nonce exists
            if (!isset($_POST['qu_analytics_nonce'])) {
                error_log('Q-Updater: Nonce not found in POST data');
                add_settings_error(
                    'q_updater_analytics',
                    'nonce_missing',
                    __('Security token missing. Please try again.', 'q-updater'),
                    'error'
                );
                return;
            }

            // Verify nonce
            $nonce = sanitize_text_field($_POST['qu_analytics_nonce']);
            $nonce_action = 'qu_analytics_settings';

            error_log('Q-Updater: Verifying nonce: ' . $nonce . ' for action: ' . $nonce_action);

            if (!wp_verify_nonce($nonce, $nonce_action)) {
                error_log('Q-Updater: Nonce verification failed');
                add_settings_error(
                    'q_updater_analytics',
                    'nonce_verification_failed',
                    __('Security verification failed. The form may have expired. Please refresh the page and try again.', 'q-updater'),
                    'error'
                );
                return;
            }

            error_log('Q-Updater: Nonce verification successful');

            // Get the developer email
            $developer_email = get_option('q_updater_developer_email', '');
            if (empty($developer_email)) {
                $developer_email = get_option('admin_email');
            }

            error_log('Q-Updater: Developer email for test report: ' . $developer_email);

            if (empty($developer_email)) {
                add_settings_error(
                    'q_updater_analytics',
                    'email_missing',
                    __('No email address configured. Please set a developer email address first.', 'q-updater'),
                    'error'
                );
                return;
            }

            // Get the analytics instance
            $analytics = $this->parent->get_analytics();

            // Send the report
            $result = $analytics->send_weekly_report();

            // Check if email was sent successfully
            if ($result) {
                // Add success message
                add_settings_error(
                    'q_updater_analytics',
                    'test_report_sent',
                    __('Test analytics report sent successfully. Please check your email.', 'q-updater'),
                    'updated'
                );

                // Log success
                error_log('Q-Updater: Test report email sent successfully to ' . $developer_email);
            } else {
                // Add error message
                add_settings_error(
                    'q_updater_analytics',
                    'test_report_failed',
                    __('Failed to send test report email. Please check your email configuration and PHP mail settings.', 'q-updater'),
                    'error'
                );

                // Log the error
                error_log('Q-Updater: Failed to send test report email to ' . $developer_email);
            }
        }
    }

    /**
     * Render the Analytics tab
     */
    private function render_analytics_tab()
    {
        // Get analytics instance
        $analytics = $this->parent->get_analytics();

        // Get plugin manager for plugin list
        $plugin_manager = new Q_Updater_Plugin_Manager($this->parent);
        $installed_plugins = $plugin_manager->get_installed_q_plugins();

        // Get analytics settings
        $analytics_settings = get_option($this->parent->get_option_name('analytics'), ['enabled' => true, 'weekly_report' => true]);
        $analytics_enabled = isset($analytics_settings['enabled']) ? $analytics_settings['enabled'] : true;
        $weekly_report_enabled = isset($analytics_settings['weekly_report']) ? $analytics_settings['weekly_report'] : true;

        // Get date range from query parameters or use defaults
        $start_date = isset($_GET['start_date']) ? sanitize_text_field($_GET['start_date']) : date('Y-m-d', strtotime('-30 days'));
        $end_date = isset($_GET['end_date']) ? sanitize_text_field($_GET['end_date']) : date('Y-m-d');
        $selected_plugin = isset($_GET['plugin']) ? sanitize_text_field($_GET['plugin']) : 'all';

        // Get analytics summary data
        $summary_data = $analytics->get_analytics_summary($start_date, $end_date);

        // Get update frequency data
        $update_frequency_data = $analytics->get_update_frequency($selected_plugin, $start_date, $end_date);

        // Get user engagement data
        $user_engagement_data = $analytics->get_user_engagement($start_date, $end_date);
        ?>
                <div class="qu-analytics-section">
                    <!-- Analytics Header -->
                    <div class="qu-settings-card">
                        <div class="qu-card-header">
                            <h2><span class="dashicons dashicons-chart-bar"></span> Plugin Analytics</h2>
                            <div class="qu-button-group">
                                <form method="post" action="" class="qu-analytics-settings-form">
                                    <?php wp_nonce_field('qu_analytics_settings', 'qu_analytics_nonce'); ?>
                                    <input type="hidden" name="analytics_enabled" value="<?php echo $analytics_enabled ? '1' : '0'; ?>">
                                    <button type="submit" class="qu-button qu-button-secondary" name="toggle_analytics">
                                        <?php echo $analytics_enabled ? 'Disable Analytics' : 'Enable Analytics'; ?>
                                    </button>
                                </form>
                                <button type="button" id="export-analytics" class="qu-button qu-button-primary">
                                    <span class="dashicons dashicons-download"></span> Export Data
                                </button>
                            </div>
                        </div>

                        <div class="qu-analytics-settings">
                            <!-- Developer Email Form -->
                            <form method="post" action="" class="qu-analytics-settings-form" style="margin-bottom: 20px;">
                                <?php wp_nonce_field('q_updater_settings', 'q_updater_nonce'); ?>
                                <h3><span class="dashicons dashicons-email"></span> Developer Email Settings</h3>
                                <div class="qu-settings-form-group">
                                    <input type="email" id="q_updater_developer_email" name="q_updater_developer_email"
                                        value="<?php echo esc_attr(get_option('q_updater_developer_email', get_option('admin_email'))); ?>"
                                        class="regular-text" placeholder="<EMAIL>" />
                                    <p class="description">
                                        This email address will receive weekly analytics reports and plugin reviews.
                                    </p>
                                </div>
                                <button type="submit" class="qu-button qu-button-primary" name="update_developer_email">
                                    <span class="dashicons dashicons-saved"></span> Save Developer Email
                                </button>
                            </form>

                            <!-- Weekly Report Settings -->
                            <form method="post" action="" class="qu-analytics-settings-form">
                                <?php wp_nonce_field('qu_analytics_settings', 'qu_analytics_nonce'); ?>
                                <h3><span class="dashicons dashicons-calendar-alt"></span> Weekly Report Settings</h3>
                                <div class="qu-checkbox-wrapper">
                                    <input type="checkbox" id="weekly-report" name="weekly_report" value="1" <?php checked($weekly_report_enabled); ?>>
                                    <label for="weekly-report">Send weekly analytics report to developer email</label>
                                    <p class="description">
                                        A summary of plugin analytics will be emailed weekly to the developer email address:
                                        <strong><?php echo esc_html(get_option('q_updater_developer_email', get_option('admin_email'))); ?></strong>
                                    </p>
                                </div>
                                <button type="submit" class="qu-button qu-button-secondary" name="update_analytics_settings">
                                    Update Settings
                                </button>
                            </form>

                            <!-- Test Report Form -->
                            <div class="qu-analytics-settings-form">
                                <h3><span class="dashicons dashicons-email-alt"></span> Test Report</h3>
                                <p class="description">
                                    Send a test analytics report to the developer email address to verify that email delivery is working
                                    correctly.
                                </p>
                                <a href="<?php echo plugins_url('test-email.php', dirname(__FILE__)); ?>"
                                    class="qu-button qu-button-secondary">
                                    <span class="dashicons dashicons-email-alt"></span> Send Test Report
                                </a>
                            </div>
                        </div>

                        <?php settings_errors('q_updater_analytics'); ?>


                    </div>

                </div>
                <?php
    }

    /**
     * Render the Tools tab
     *
     * @param Q_Updater_Plugin_Manager $plugin_manager Plugin manager instance
     */
    private function render_tools_tab($plugin_manager)
    {
        ?>
                <div class="qu-tools-section">
                    <!-- Tools Navigation -->
                    <div class="qu-tools-nav">
                        <a href="#rollback" class="qu-tools-nav-item active">
                            Plugin Rollback
                        </a>
                        <a href="#backups" class="qu-tools-nav-item">
                            Local Backups
                        </a>
                        <a href="#advanced-tools" class="qu-tools-nav-item">
                            Advanced Tools
                        </a>
                    </div>

                    <!-- Rollback Section -->
                    <div id="rollback" class="qu-tools-content">
                        <div class="qu-settings-card qu-rollback-card">
                            <h2><span class="dashicons dashicons-backup"></span> Plugin Rollback</h2>

                            <div class="qu-settings-form-group">
                                <label for="rollback-plugin-select">
                                    Select Plugin
                                    <span class="qu-settings-tooltip">
                                        <span class="dashicons dashicons-editor-help"></span>
                                        <span class="qu-tooltip-content">
                                            Choose a plugin to view its available versions for rollback.
                                            This will show all releases from the GitHub repository.
                                        </span>
                                    </span>
                                </label>
                                <select id="rollback-plugin-select" class="regular-text">
                                    <option value="">Select a plugin</option>
                                    <?php foreach ($plugin_manager->get_installed_q_plugins() as $slug => $repo) {
                                        $plugin_data = $plugin_manager->get_safe_plugin_data("$slug/$slug.php");
                                        if ($plugin_data) {
                                            echo sprintf(
                                                '<option value="%s" data-repo="%s" data-current="%s">%s (Current: %s)</option>',
                                                esc_attr($slug),
                                                esc_attr($repo),
                                                esc_attr($plugin_data['Version']),
                                                esc_html($plugin_data['Name']),
                                                esc_html($plugin_data['Version'])
                                            );
                                        }
                                    } ?>
                                </select>
                                <p class="description">Select a plugin to view available versions for rollback</p>
                            </div>

                            <div id="rollback-versions-container" class="qu-rollback-versions"></div>

                            <div class="qu-rollback-warning">
                                <div class="qu-warning-icon">
                                    <span class="dashicons dashicons-warning"></span>
                                </div>
                                <div class="qu-warning-content">
                                    <h3>Warning: Use Rollback with Caution</h3>
                                    <p>Rolling back to an older version may cause compatibility issues or loss of data. Always backup
                                        your
                                        site before performing a rollback.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Backups Section -->
                    <div id="backups" class="qu-tools-content" style="display: none;">
                        <div class="qu-settings-card">
                            <h2><span class="dashicons dashicons-archive"></span> Local Plugin Backups</h2>

                            <div class="qu-settings-form-group">
                                <p>Q-Updater automatically creates local backups of plugins before updating them. You can restore from
                                    these backups if you encounter issues after an update.</p>

                                <label for="backup-plugin-select">
                                    Select Plugin
                                    <span class="qu-settings-tooltip">
                                        <span class="dashicons dashicons-editor-help"></span>
                                        <span class="qu-tooltip-content">
                                            Choose a plugin to view its available backups.
                                        </span>
                                    </span>
                                </label>
                                <select id="backup-plugin-select" class="regular-text">
                                    <option value="">Select a plugin</option>
                                    <?php
                                    $backups = get_option($this->parent->get_option_name('backups'), []);
                                    foreach ($backups as $slug => $plugin_backups) {
                                        if (!empty($plugin_backups)) {
                                            $plugin_data = $plugin_manager->get_safe_plugin_data("$slug/$slug.php");
                                            $plugin_name = $plugin_data ? $plugin_data['Name'] : $slug;
                                            echo sprintf(
                                                '<option value="%s">%s (%d backups)</option>',
                                                esc_attr($slug),
                                                esc_html($plugin_name),
                                                count($plugin_backups)
                                            );
                                        }
                                    }
                                    ?>
                                </select>
                            </div>

                            <div id="plugin-backups-container" class="qu-plugin-backups">
                                <div class="qu-empty-state">
                                    <span class="dashicons dashicons-archive"></span>
                                    <p>Select a plugin to view available backups</p>
                                </div>
                            </div>

                            <div class="qu-rollback-warning">
                                <div class="qu-warning-icon">
                                    <span class="dashicons dashicons-warning"></span>
                                </div>
                                <div class="qu-warning-content">
                                    <h3>Warning: Use Restoration with Caution</h3>
                                    <p>Restoring from a backup may cause compatibility issues with other plugins or themes. Always test
                                        on a staging environment first if possible.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Tools Section -->
                    <div id="advanced-tools" class="qu-tools-content" style="display: none;">
                        <div class="qu-settings-card">
                            <h2><span class="dashicons dashicons-admin-tools"></span> Advanced Tools</h2>

                            <div class="qu-group">

                                <div class="qu-settings-form-group">
                                    <h3>Plugin Cache Management</h3>
                                    <p>Clear cached plugin data to force fresh checks with GitHub repositories.</p>
                                    <button type="button" id="clear-plugin-cache" class="qu-button qu-button-secondary">
                                        <span class="dashicons dashicons-trash"></span> Clear Plugin Cache
                                    </button>
                                </div>

                                <div class="qu-settings-form-group">
                                    <h3>Plugin Diagnostics</h3>
                                    <p>Run diagnostics to check for potential issues with your Q plugins.</p>
                                    <button type="button" id="run-plugin-diagnostics" class="qu-button qu-button-secondary">
                                        <span class="dashicons dashicons-chart-bar"></span> Run Diagnostics
                                    </button>
                                </div>


                            </div>

                            <div id="advanced-tools-status" class="qu-advanced-tools-status"></div>
                        </div>
                    </div>
                </div>
                <?php
    }


}
